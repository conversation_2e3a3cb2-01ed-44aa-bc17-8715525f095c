---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Headless service for PostgreSQL StatefulSet"
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
spec:
  type: ClusterIP
  clusterIP: None  # Headless service for StatefulSet
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
  sessionAffinity: None
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service-lb
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-service-lb
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Load balancer service for PostgreSQL (for external access if needed)"
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "database-subnet"
spec:
  type: ClusterIP  # Change to LoadBalancer if external access is needed
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
