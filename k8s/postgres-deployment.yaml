---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: phoenix-application
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: managed-premium
  resources:
    requests:
      storage: 32Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: phoenix-application
    app.kubernetes.io/version: "14"
  annotations:
    description: "PostgreSQL database for Phoenix application"
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres
        app.kubernetes.io/component: database
        app.kubernetes.io/part-of: phoenix-application
        app.kubernetes.io/version: "14"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9187"
    spec:
      # Security context for the pod
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsNonRoot: true
      
      # Node affinity for Azure-specific node pools
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: agentpool
                operator: In
                values:
                - database
          - weight: 50
            preference:
              matchExpressions:
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
      
      containers:
      - name: postgres
        image: postgres:14-alpine
        imagePullPolicy: IfNotPresent
        
        # Resource management
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        
        # Environment variables
        env:
        - name: POSTGRES_DB
          value: "phoenix_prod"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DB_USERNAME
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DB_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        
        # Health checks
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 6
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        # Volume mounts
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
          readOnly: true
        
        # Ports
        ports:
        - name: postgres
          containerPort: 5432
          protocol: TCP
      
      # Volumes
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: postgres-config
        configMap:
          name: postgres-config
          items:
          - key: postgresql.conf
            path: postgresql.conf
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-config
data:
  postgresql.conf: |
    # PostgreSQL configuration optimized for Kubernetes
    listen_addresses = '*'
    port = 5432
    max_connections = 100
    shared_buffers = 256MB
    effective_cache_size = 1GB
    maintenance_work_mem = 64MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 4MB
    min_wal_size = 1GB
    max_wal_size = 4GB
    max_worker_processes = 8
    max_parallel_workers_per_gather = 2
    max_parallel_workers = 8
    max_parallel_maintenance_workers = 2
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # Security
    ssl = off
    password_encryption = scram-sha-256
