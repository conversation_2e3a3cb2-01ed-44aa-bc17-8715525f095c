---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: phoenix-app-sa
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: service-account
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Service account for Phoenix application with Azure integrations"
    # Azure Workload Identity annotations
    azure.workload.identity/client-id: "{{AZURE_CLIENT_ID}}"
    azure.workload.identity/tenant-id: "{{AZURE_TENANT_ID}}"
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: phoenix-app-role
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: role
rules:
# Allow reading ConfigMaps and Secrets
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]

# Allow reading pod information for clustering
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

# Allow reading services for service discovery
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]

# Allow creating events for logging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

# Allow reading node information for telemetry
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list"]

# Allow reading persistent volume claims
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: phoenix-app-rolebinding
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: role-binding
subjects:
- kind: ServiceAccount
  name: phoenix-app-sa
  namespace: phoenix-app
roleRef:
  kind: Role
  name: phoenix-app-role
  apiGroup: rbac.authorization.k8s.io
---
# ClusterRole for cross-namespace operations (if needed)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: phoenix-app-cluster-role
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: cluster-role
rules:
# Allow reading nodes for clustering and telemetry
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]

# Allow reading namespaces for multi-tenant operations
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]

# Allow reading custom resource definitions for operators
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list", "watch"]

# Allow reading metrics for autoscaling
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: phoenix-app-cluster-rolebinding
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: cluster-role-binding
subjects:
- kind: ServiceAccount
  name: phoenix-app-sa
  namespace: phoenix-app
roleRef:
  kind: ClusterRole
  name: phoenix-app-cluster-role
  apiGroup: rbac.authorization.k8s.io
---
# Service account for monitoring components
apiVersion: v1
kind: ServiceAccount
metadata:
  name: phoenix-monitoring-sa
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-monitoring
    app.kubernetes.io/component: monitoring-service-account
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: phoenix-monitoring-role
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-monitoring
    app.kubernetes.io/component: monitoring-role
rules:
# Allow reading all resources for monitoring
- apiGroups: [""]
  resources: ["*"]
  verbs: ["get", "list", "watch"]

# Allow reading apps resources
- apiGroups: ["apps"]
  resources: ["*"]
  verbs: ["get", "list", "watch"]

# Allow reading extensions resources
- apiGroups: ["extensions"]
  resources: ["*"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: phoenix-monitoring-rolebinding
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-monitoring
    app.kubernetes.io/component: monitoring-role-binding
subjects:
- kind: ServiceAccount
  name: phoenix-monitoring-sa
  namespace: phoenix-app
roleRef:
  kind: Role
  name: phoenix-monitoring-role
  apiGroup: rbac.authorization.k8s.io
