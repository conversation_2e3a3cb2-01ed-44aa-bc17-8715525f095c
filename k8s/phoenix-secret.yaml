---
apiVersion: v1
kind: Secret
metadata:
  name: phoenix-secrets
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: secret
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Sensitive configuration for Phoenix application"
type: Opaque
data:
  # Database credentials (base64 encoded)
  # Replace these with actual base64 encoded values
  DATABASE_URL: "{{DATABASE_URL_BASE64}}"
  DB_USERNAME: "{{DB_USERNAME_BASE64}}"
  DB_PASSWORD: "{{DB_PASSWORD_BASE64}}"
  
  # Phoenix secret key base (base64 encoded)
  SECRET_KEY_BASE: "{{SECRET_KEY_BASE_BASE64}}"
  
  # LiveView signing salt (base64 encoded)
  LIVE_VIEW_SIGNING_SALT_SECRET: "{{LIVE_VIEW_SIGNING_SALT_BASE64}}"
  
  # Guardian secret key (base64 encoded)
  GUARDIAN_SECRET_KEY: "{{GUARDIAN_SECRET_KEY_BASE64}}"
  
  # Redis password (if authentication is enabled)
  REDIS_PASSWORD: "{{REDIS_PASSWORD_BASE64}}"
  
  # External API keys (base64 encoded)
  EXTERNAL_API_KEY: "{{EXTERNAL_API_KEY_BASE64}}"
  
  # JWT signing key (base64 encoded)
  JWT_SECRET: "{{JWT_SECRET_BASE64}}"
  
  # Encryption key for sensitive data (base64 encoded)
  ENCRYPTION_KEY: "{{ENCRYPTION_KEY_BASE64}}"
  
  # Azure Storage access key (base64 encoded)
  AZURE_STORAGE_ACCESS_KEY: "{{AZURE_STORAGE_ACCESS_KEY_BASE64}}"
  
  # Email service credentials (base64 encoded)
  SMTP_USERNAME: "{{SMTP_USERNAME_BASE64}}"
  SMTP_PASSWORD: "{{SMTP_PASSWORD_BASE64}}"
  
  # OAuth credentials (base64 encoded)
  OAUTH_CLIENT_ID: "{{OAUTH_CLIENT_ID_BASE64}}"
  OAUTH_CLIENT_SECRET: "{{OAUTH_CLIENT_SECRET_BASE64}}"
  
  # Monitoring and APM keys (base64 encoded)
  APM_SECRET_TOKEN: "{{APM_SECRET_TOKEN_BASE64}}"
  
  # SSL/TLS certificates (if needed)
  TLS_CERT: "{{TLS_CERT_BASE64}}"
  TLS_KEY: "{{TLS_KEY_BASE64}}"
