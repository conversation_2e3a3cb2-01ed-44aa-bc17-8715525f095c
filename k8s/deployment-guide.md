# Phoenix Elixir Application - Kubernetes Deployment Guide

This guide provides comprehensive instructions for deploying a Phoenix Elixir application on Azure Kubernetes Service (AKS) using the provided Kubernetes manifests.

## Prerequisites

### Required Tools
- `kubectl` - Kubernetes command-line tool
- `helm` - Kubernetes package manager
- `az` - Azure CLI
- `docker` - Container runtime

### Azure Resources
- Azure Kubernetes Service (AKS) cluster
- Azure Container Registry (ACR)
- Azure DNS Zone (for custom domain)
- Azure Application Gateway (for ingress)
- Azure Storage Account (for persistent storage)

## Configuration Placeholders

Before deploying, replace the following placeholders in the YAML files:

### Application Configuration
- `{{PHOENIX_IMAGE}}` - Your Phoenix application container image
- `{{DOMAIN_NAME}}` - Your application domain (e.g., myapp.com)
- `{{LETSENCRYPT_EMAIL}}` - Email for Let's Encrypt certificates

### Database Configuration
- `{{DATABASE_URL_BASE64}}` - Base64 encoded database connection string
- `{{DB_USERNAME_BASE64}}` - Base64 encoded database username
- `{{DB_PASSWORD_BASE64}}` - Base64 encoded database password

### Security Configuration
- `{{SECRET_KEY_BASE_BASE64}}` - Base64 encoded Phoenix secret key
- `{{LIVE_VIEW_SIGNING_SALT_BASE64}}` - Base64 encoded LiveView signing salt
- `{{GUARDIAN_SECRET_KEY_BASE64}}` - Base64 encoded Guardian secret key
- `{{JWT_SECRET_BASE64}}` - Base64 encoded JWT secret
- `{{ENCRYPTION_KEY_BASE64}}` - Base64 encoded encryption key

### Azure Configuration
- `{{AZURE_SUBSCRIPTION_ID}}` - Azure subscription ID
- `{{AZURE_RESOURCE_GROUP}}` - Azure resource group name
- `{{AZURE_CLIENT_ID}}` - Azure service principal client ID
- `{{AZURE_TENANT_ID}}` - Azure tenant ID
- `{{AZURE_STORAGE_ACCOUNT}}` - Azure storage account name
- `{{AZURE_REGION}}` - Azure region (e.g., eastus)

## Deployment Steps

### 1. Prepare the Environment

```bash
# Connect to your AKS cluster
az aks get-credentials --resource-group <resource-group> --name <cluster-name>

# Verify connection
kubectl cluster-info
```

### 2. Create Namespace and RBAC

```bash
# Apply namespace and resource quotas
kubectl apply -f k8s/namespace.yaml

# Apply RBAC configuration
kubectl apply -f k8s/rbac.yaml
```

### 3. Configure Storage Classes

```bash
# Apply Azure storage classes
kubectl apply -f k8s/azure-storage.yaml
```

### 4. Deploy Configuration

```bash
# Apply ConfigMap (update placeholders first)
kubectl apply -f k8s/phoenix-configmap.yaml

# Apply Secrets (update placeholders first)
kubectl apply -f k8s/phoenix-secret.yaml
```

### 5. Deploy Database Layer

```bash
# Deploy PostgreSQL
kubectl apply -f k8s/postgres-deployment.yaml
kubectl apply -f k8s/postgres-service.yaml

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n phoenix-app --timeout=300s
```

### 6. Deploy Cache Layer

```bash
# Deploy Redis
kubectl apply -f k8s/redis-deployment.yaml
kubectl apply -f k8s/redis-service.yaml

# Wait for Redis to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n phoenix-app --timeout=300s
```

### 7. Deploy Phoenix Application

```bash
# Deploy Phoenix application
kubectl apply -f k8s/phoenix-deployment.yaml
kubectl apply -f k8s/phoenix-service.yaml

# Wait for Phoenix to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=phoenix-app -n phoenix-app --timeout=300s
```

### 8. Configure Autoscaling

```bash
# Apply Horizontal Pod Autoscaler
kubectl apply -f k8s/phoenix-hpa.yaml

# Apply Pod Disruption Budget
kubectl apply -f k8s/pdb.yaml
```

### 9. Deploy Scheduled Jobs

```bash
# Apply CronJobs for background tasks
kubectl apply -f k8s/phoenix-scheduler.yaml
```

### 10. Configure Networking

```bash
# Apply Network Policies
kubectl apply -f k8s/network-policy.yaml

# Apply Ingress (ensure Application Gateway is configured)
kubectl apply -f k8s/phoenix-ingress.yaml
```

### 11. Setup Monitoring

```bash
# Apply monitoring configuration
kubectl apply -f k8s/monitoring.yaml
```

## Verification

### Check Deployment Status

```bash
# Check all resources in the namespace
kubectl get all -n phoenix-app

# Check pod status
kubectl get pods -n phoenix-app -o wide

# Check services
kubectl get services -n phoenix-app

# Check ingress
kubectl get ingress -n phoenix-app
```

### Check Application Health

```bash
# Port forward to test locally
kubectl port-forward service/phoenix-service 8080:80 -n phoenix-app

# Test health endpoint
curl http://localhost:8080/health

# Check logs
kubectl logs -f deployment/phoenix-app -n phoenix-app
```

### Check Database Connectivity

```bash
# Test database connection
kubectl exec -it deployment/phoenix-app -n phoenix-app -- /app/bin/phoenix_app eval "Phoenix.App.Repo.query!(\"SELECT 1\")"
```

### Check Cache Connectivity

```bash
# Test Redis connection
kubectl exec -it deployment/redis -n phoenix-app -- redis-cli ping
```

## Scaling Operations

### Manual Scaling

```bash
# Scale Phoenix application
kubectl scale deployment phoenix-app --replicas=5 -n phoenix-app

# Check HPA status
kubectl get hpa -n phoenix-app
```

### Update Application

```bash
# Update image
kubectl set image deployment/phoenix-app phoenix-app={{NEW_PHOENIX_IMAGE}} -n phoenix-app

# Check rollout status
kubectl rollout status deployment/phoenix-app -n phoenix-app

# Rollback if needed
kubectl rollout undo deployment/phoenix-app -n phoenix-app
```

## Troubleshooting

### Common Issues

1. **Pod Startup Issues**
   ```bash
   kubectl describe pod <pod-name> -n phoenix-app
   kubectl logs <pod-name> -n phoenix-app
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it deployment/postgres -n phoenix-app -- psql -U <username> -d <database>
   ```

3. **Network Policy Issues**
   ```bash
   kubectl describe networkpolicy -n phoenix-app
   ```

4. **Ingress Issues**
   ```bash
   kubectl describe ingress phoenix-ingress -n phoenix-app
   kubectl logs -n kube-system -l app=ingress-azure
   ```

### Monitoring and Logs

```bash
# View application logs
kubectl logs -f deployment/phoenix-app -n phoenix-app

# View database logs
kubectl logs -f statefulset/postgres -n phoenix-app

# View cache logs
kubectl logs -f deployment/redis -n phoenix-app

# View scheduler logs
kubectl logs -f job/<job-name> -n phoenix-app
```

## Security Considerations

1. **Secrets Management**: Use Azure Key Vault for sensitive data
2. **Network Policies**: Ensure proper network segmentation
3. **RBAC**: Follow principle of least privilege
4. **Image Security**: Scan container images for vulnerabilities
5. **TLS**: Ensure all communications are encrypted

## Maintenance

### Regular Tasks

1. **Update Dependencies**: Regularly update container images
2. **Backup Database**: Implement automated backup strategy
3. **Monitor Resources**: Set up alerts for resource usage
4. **Security Updates**: Apply security patches promptly
5. **Certificate Renewal**: Monitor TLS certificate expiration

### Backup and Recovery

```bash
# Database backup
kubectl exec -it deployment/postgres -n phoenix-app -- pg_dump -U <username> <database> > backup.sql

# Restore database
kubectl exec -i deployment/postgres -n phoenix-app -- psql -U <username> <database> < backup.sql
```

This deployment configuration provides a production-ready setup for Phoenix Elixir applications on Azure Kubernetes Service with high availability, security, and monitoring capabilities.
