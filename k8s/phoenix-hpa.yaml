---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: phoenix-app-hpa
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: autoscaler
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Horizontal Pod Autoscaler for Phoenix application"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: phoenix-app
  
  # Scaling configuration
  minReplicas: 3
  maxReplicas: 10
  
  # Scaling behavior
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
      - type: Percent
        value: 50
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 300
      - type: Percent
        value: 10
        periodSeconds: 300
      selectPolicy: Min
  
  # Metrics for scaling decisions
  metrics:
  # CPU utilization target
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  # Memory utilization target
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # Custom metrics (if available)
  # - type: Pods
  #   pods:
  #     metric:
  #       name: http_requests_per_second
  #     target:
  #       type: AverageValue
  #       averageValue: "100"
  
  # External metrics (Azure Monitor integration)
  # - type: External
  #   external:
  #     metric:
  #       name: azure_application_gateway_requests_per_minute
  #       selector:
  #         matchLabels:
  #           resource_group: "{{AZURE_RESOURCE_GROUP}}"
  #     target:
  #       type: AverageValue
  #       averageValue: "1000"
