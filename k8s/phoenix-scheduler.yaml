---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: phoenix-scheduler
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-scheduler
    app.kubernetes.io/component: scheduler
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "CronJob for Phoenix application background tasks and scheduled jobs"
spec:
  # Schedule (configurable via environment variable, default: every hour)
  schedule: "0 * * * *"  # Every hour at minute 0
  
  # Timezone (optional, defaults to UTC)
  timeZone: "UTC"
  
  # Job history limits
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  
  # Concurrency policy
  concurrencyPolicy: Forbid  # Prevent overlapping jobs
  
  # Starting deadline
  startingDeadlineSeconds: 300  # 5 minutes
  
  # Suspend jobs (useful for maintenance)
  suspend: false
  
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: phoenix-scheduler
        app.kubernetes.io/component: scheduler-job
        app.kubernetes.io/part-of: phoenix-application
    spec:
      # Job completion and parallelism
      completions: 1
      parallelism: 1
      
      # Backoff limit for failed jobs
      backoffLimit: 3
      
      # Active deadline for job execution
      activeDeadlineSeconds: 3600  # 1 hour
      
      # TTL for finished jobs
      ttlSecondsAfterFinished: 86400  # 24 hours
      
      template:
        metadata:
          labels:
            app.kubernetes.io/name: phoenix-scheduler
            app.kubernetes.io/component: scheduler-pod
            app.kubernetes.io/part-of: phoenix-application
          annotations:
            description: "Scheduled job pod for Phoenix background tasks"
        spec:
          # Security context
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            fsGroup: 1000
          
          # Service account
          serviceAccountName: phoenix-app-sa
          
          # Node affinity
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                preference:
                  matchExpressions:
                  - key: agentpool
                    operator: In
                    values:
                    - application
              - weight: 50
                preference:
                  matchExpressions:
                  - key: kubernetes.io/os
                    operator: In
                    values:
                    - linux
          
          containers:
          - name: scheduler
            image: "{{PHOENIX_IMAGE}}"
            imagePullPolicy: IfNotPresent
            
            # Command to run scheduled tasks
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting scheduled tasks..."
              /app/bin/phoenix_app eval "Phoenix.App.Scheduler.run_scheduled_tasks()"
              echo "Scheduled tasks completed."
            
            # Resource management
            resources:
              requests:
                cpu: 200m
                memory: 512Mi
              limits:
                cpu: 500m
                memory: 1Gi
            
            # Environment variables from ConfigMap
            envFrom:
            - configMapRef:
                name: phoenix-config
            
            # Environment variables from Secrets
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: DATABASE_URL
            - name: SECRET_KEY_BASE
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: SECRET_KEY_BASE
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: REDIS_PASSWORD
                  optional: true
            - name: EXTERNAL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: EXTERNAL_API_KEY
                  optional: true
            - name: JOB_TYPE
              value: "scheduled"
            - name: SCHEDULER_ENABLED
              value: "true"
            
            # Security context for the container
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: false
              runAsNonRoot: true
              runAsUser: 1000
              capabilities:
                drop:
                - ALL
            
            # Volume mounts
            volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: job-logs
              mountPath: /app/logs
          
          # Volumes
          volumes:
          - name: tmp
            emptyDir: {}
          - name: job-logs
            emptyDir: {}
          
          # Restart policy for jobs
          restartPolicy: OnFailure
          
          # Termination grace period
          terminationGracePeriodSeconds: 30
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: phoenix-cleanup
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-cleanup
    app.kubernetes.io/component: cleanup-scheduler
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "CronJob for Phoenix application cleanup tasks"
spec:
  # Schedule: Daily at 2 AM UTC
  schedule: "0 2 * * *"
  timeZone: "UTC"
  
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  concurrencyPolicy: Forbid
  startingDeadlineSeconds: 600
  suspend: false
  
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: phoenix-cleanup
        app.kubernetes.io/component: cleanup-job
    spec:
      completions: 1
      parallelism: 1
      backoffLimit: 2
      activeDeadlineSeconds: 1800  # 30 minutes
      ttlSecondsAfterFinished: 86400
      
      template:
        metadata:
          labels:
            app.kubernetes.io/name: phoenix-cleanup
            app.kubernetes.io/component: cleanup-pod
        spec:
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            fsGroup: 1000
          
          serviceAccountName: phoenix-app-sa
          
          containers:
          - name: cleanup
            image: "{{PHOENIX_IMAGE}}"
            imagePullPolicy: IfNotPresent
            
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting cleanup tasks..."
              /app/bin/phoenix_app eval "Phoenix.App.Cleanup.run_daily_cleanup()"
              echo "Cleanup tasks completed."
            
            resources:
              requests:
                cpu: 100m
                memory: 256Mi
              limits:
                cpu: 300m
                memory: 512Mi
            
            envFrom:
            - configMapRef:
                name: phoenix-config
            
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: DATABASE_URL
            - name: SECRET_KEY_BASE
              valueFrom:
                secretKeyRef:
                  name: phoenix-secrets
                  key: SECRET_KEY_BASE
            - name: JOB_TYPE
              value: "cleanup"
            
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: false
              runAsNonRoot: true
              runAsUser: 1000
              capabilities:
                drop:
                - ALL
          
          restartPolicy: OnFailure
          terminationGracePeriodSeconds: 30
