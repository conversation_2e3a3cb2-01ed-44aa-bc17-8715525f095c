---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: phoenix-app
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-server
    app.kubernetes.io/part-of: phoenix-application
    app.kubernetes.io/version: "latest"
  annotations:
    description: "Phoenix Elixir web application deployment"
    deployment.kubernetes.io/revision: "1"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: phoenix-app
      app.kubernetes.io/component: web-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: phoenix-app
        app.kubernetes.io/component: web-server
        app.kubernetes.io/part-of: phoenix-application
        app.kubernetes.io/version: "latest"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      # Security context for the pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      
      # Service account for Azure integrations
      serviceAccountName: phoenix-app-sa
      
      # Node affinity for Azure-specific node pools
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: agentpool
                operator: In
                values:
                - application
          - weight: 50
            preference:
              matchExpressions:
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
        
        # Pod anti-affinity to spread replicas across nodes
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - phoenix-app
              topologyKey: kubernetes.io/hostname
      
      # Init container for database migrations
      initContainers:
      - name: migrate
        image: "{{PHOENIX_IMAGE}}"
        imagePullPolicy: IfNotPresent
        
        command:
        - /bin/sh
        - -c
        - |
          echo "Running database migrations..."
          /app/bin/phoenix_app eval "Phoenix.App.Release.migrate()"
        
        env:
        - name: MIX_ENV
          valueFrom:
            configMapKeyRef:
              name: phoenix-config
              key: MIX_ENV
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DATABASE_URL
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: SECRET_KEY_BASE
        
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      
      containers:
      - name: phoenix-app
        image: "{{PHOENIX_IMAGE}}"
        imagePullPolicy: IfNotPresent
        
        # Resource management
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        
        # Environment variables from ConfigMap
        envFrom:
        - configMapRef:
            name: phoenix-config
        
        # Environment variables from Secrets
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DATABASE_URL
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: SECRET_KEY_BASE
        - name: LIVE_VIEW_SIGNING_SALT_SECRET
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: LIVE_VIEW_SIGNING_SALT_SECRET
        - name: GUARDIAN_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: GUARDIAN_SECRET_KEY
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: REDIS_PASSWORD
              optional: true
        - name: EXTERNAL_API_KEY
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: EXTERNAL_API_KEY
              optional: true
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: JWT_SECRET
              optional: true
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: ENCRYPTION_KEY
              optional: true
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        
        # Startup probe for slow-starting applications
        startupProbe:
          httpGet:
            path: /health
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        
        # Ports
        ports:
        - name: http
          containerPort: 4000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        
        # Security context for the container
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        # Volume mounts for temporary files and logs
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
      
      # Volumes
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app-logs
        emptyDir: {}
      
      # DNS configuration
      dnsPolicy: ClusterFirst
      
      # Restart policy
      restartPolicy: Always
      
      # Termination grace period
      terminationGracePeriodSeconds: 30
