---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: phoenix-application
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: managed-premium
  resources:
    requests:
      storage: 8Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-config
data:
  redis.conf: |
    # Redis configuration optimized for Kubernetes and caching
    bind 0.0.0.0
    port 6379
    protected-mode no
    
    # Memory management
    maxmemory 200mb
    maxmemory-policy allkeys-lru
    
    # Persistence (for cache durability)
    save 900 1
    save 300 10
    save 60 10000
    
    # Logging
    loglevel notice
    logfile ""
    
    # Performance tuning
    tcp-keepalive 300
    timeout 0
    tcp-backlog 511
    
    # Security (password will be set via environment variable)
    # requirepass will be set via REDIS_PASSWORD env var
    
    # Append only file
    appendonly yes
    appendfsync everysec
    
    # Disable dangerous commands
    rename-command FLUSHDB ""
    rename-command FLUSHALL ""
    rename-command DEBUG ""
    rename-command CONFIG ""
    rename-command SHUTDOWN SHUTDOWN_REDIS
    rename-command EVAL ""
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
    app.kubernetes.io/part-of: phoenix-application
    app.kubernetes.io/version: "7-alpine"
  annotations:
    description: "Redis cache for Phoenix application using Nebulex"
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: cache
  template:
    metadata:
      labels:
        app.kubernetes.io/name: redis
        app.kubernetes.io/component: cache
        app.kubernetes.io/part-of: phoenix-application
        app.kubernetes.io/version: "7-alpine"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
    spec:
      # Security context
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsNonRoot: true
      
      # Node affinity for Azure-specific node pools
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: agentpool
                operator: In
                values:
                - cache
          - weight: 50
            preference:
              matchExpressions:
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
      
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        
        # Resource management
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 200m
            memory: 512Mi
        
        # Command and arguments
        command:
        - redis-server
        - /etc/redis/redis.conf
        
        # Environment variables
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: REDIS_PASSWORD
              optional: true
        
        # Health checks
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        
        # Volume mounts
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis/redis.conf
          subPath: redis.conf
          readOnly: true
        
        # Ports
        ports:
        - name: redis
          containerPort: 6379
          protocol: TCP
      
      # Redis Exporter for Prometheus monitoring
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        
        env:
        - name: REDIS_ADDR
          value: "redis://localhost:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: REDIS_PASSWORD
              optional: true
        
        ports:
        - name: metrics
          containerPort: 9121
          protocol: TCP
        
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 30
          periodSeconds: 30
        
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 5
          periodSeconds: 10
      
      # Volumes
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config
          items:
          - key: redis.conf
            path: redis.conf
