---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: phoenix-app-metrics
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: service-monitor
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "ServiceMonitor for Phoenix application metrics collection"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: phoenix-app
      app.kubernetes.io/component: web-service
  
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    
    # Relabeling configuration
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_node_name]
      targetLabel: node
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: namespace
    
    # Metric relabeling
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'phoenix_.*'
      action: keep
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: postgres-metrics
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-service-monitor
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
  
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: redis-metrics
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-service-monitor
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
  
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-phoenix
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: dashboard
    grafana_dashboard: "1"
data:
  phoenix-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Phoenix Application Dashboard",
        "tags": ["phoenix", "elixir", "kubernetes"],
        "timezone": "UTC",
        "panels": [
          {
            "id": 1,
            "title": "HTTP Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(phoenix_http_requests_total[5m])",
                "legendFormat": "{{method}} {{status}}"
              }
            ]
          },
          {
            "id": 2,
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(phoenix_http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "95th percentile"
              }
            ]
          },
          {
            "id": 3,
            "title": "Active Connections",
            "type": "singlestat",
            "targets": [
              {
                "expr": "phoenix_endpoint_active_connections",
                "legendFormat": "Active Connections"
              }
            ]
          },
          {
            "id": 4,
            "title": "Database Connections",
            "type": "graph",
            "targets": [
              {
                "expr": "ecto_connections_active",
                "legendFormat": "Active DB Connections"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules-phoenix
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: rules
data:
  phoenix.rules.yml: |
    groups:
    - name: phoenix.rules
      rules:
      # High error rate alert
      - alert: PhoenixHighErrorRate
        expr: rate(phoenix_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Phoenix application has error rate of {{ $value }} errors per second"
      
      # High response time alert
      - alert: PhoenixHighResponseTime
        expr: histogram_quantile(0.95, rate(phoenix_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"
      
      # Database connection pool exhaustion
      - alert: PhoenixDatabasePoolExhaustion
        expr: ecto_connections_active / ecto_connections_max > 0.9
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Database connection pool is {{ $value | humanizePercentage }} full"
      
      # Pod restart alert
      - alert: PhoenixPodRestarts
        expr: increase(kube_pod_container_status_restarts_total{namespace="phoenix-app"}[1h]) > 3
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Phoenix pod restarting frequently"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last hour"
      
      # Memory usage alert
      - alert: PhoenixHighMemoryUsage
        expr: container_memory_usage_bytes{namespace="phoenix-app",container="phoenix-app"} / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Phoenix application memory usage is {{ $value | humanizePercentage }}"
      
      # CPU usage alert
      - alert: PhoenixHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{namespace="phoenix-app",container="phoenix-app"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "Phoenix application CPU usage is {{ $value | humanizePercentage }}"
