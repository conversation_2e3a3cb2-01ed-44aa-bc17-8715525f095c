---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-premium-ssd
  labels:
    app.kubernetes.io/name: azure-storage
    app.kubernetes.io/component: storage-class
  annotations:
    description: "Premium SSD storage class for high-performance workloads"
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: disk.csi.azure.com
parameters:
  skuName: Premium_LRS
  kind: managed
  cachingMode: ReadOnly
  fsType: ext4
  
# Volume expansion and binding
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer

# Reclaim policy
reclaimPolicy: Retain
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-standard-ssd
  labels:
    app.kubernetes.io/name: azure-storage
    app.kubernetes.io/component: storage-class
  annotations:
    description: "Standard SSD storage class for general workloads"
provisioner: disk.csi.azure.com
parameters:
  skuName: StandardSSD_LRS
  kind: managed
  cachingMode: ReadOnly
  fsType: ext4

allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: azure-files-premium
  labels:
    app.kubernetes.io/name: azure-storage
    app.kubernetes.io/component: storage-class-files
  annotations:
    description: "Premium Azure Files storage class for shared storage"
provisioner: file.csi.azure.com
parameters:
  skuName: Premium_LRS
  protocol: SMB
  
allowVolumeExpansion: true
volumeBindingMode: Immediate
reclaimPolicy: Delete
---
# Secret for Azure Storage Account (if using Azure Files or Blob)
apiVersion: v1
kind: Secret
metadata:
  name: azure-storage-secret
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: azure-storage
    app.kubernetes.io/component: storage-secret
type: Opaque
data:
  # Base64 encoded Azure Storage Account credentials
  azurestorageaccountname: "{{AZURE_STORAGE_ACCOUNT_NAME_BASE64}}"
  azurestorageaccountkey: "{{AZURE_STORAGE_ACCOUNT_KEY_BASE64}}"
---
# PersistentVolume for shared file storage (if needed)
apiVersion: v1
kind: PersistentVolume
metadata:
  name: phoenix-shared-storage
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: shared-storage
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: azure-files-premium
  csi:
    driver: file.csi.azure.com
    readOnly: false
    volumeHandle: phoenix-shared-storage
    volumeAttributes:
      resourceGroup: "{{AZURE_RESOURCE_GROUP}}"
      storageAccount: "{{AZURE_STORAGE_ACCOUNT}}"
      shareName: "phoenix-shared"
      protocol: "smb"
    nodeStageSecretRef:
      name: azure-storage-secret
      namespace: phoenix-app
---
# PersistentVolumeClaim for shared storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: phoenix-shared-pvc
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: shared-storage-claim
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: azure-files-premium
  resources:
    requests:
      storage: 100Gi
---
# Azure Blob Storage configuration (using CSI driver)
apiVersion: v1
kind: PersistentVolume
metadata:
  name: phoenix-blob-storage
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: blob-storage
spec:
  capacity:
    storage: 1000Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: blob.csi.azure.com
    readOnly: false
    volumeHandle: phoenix-blob-storage
    volumeAttributes:
      resourceGroup: "{{AZURE_RESOURCE_GROUP}}"
      storageAccount: "{{AZURE_STORAGE_ACCOUNT}}"
      containerName: "phoenix-uploads"
      protocol: "fuse"
    nodeStageSecretRef:
      name: azure-storage-secret
      namespace: phoenix-app
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: phoenix-blob-pvc
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: blob-storage-claim
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: ""
  volumeName: phoenix-blob-storage
  resources:
    requests:
      storage: 1000Gi
