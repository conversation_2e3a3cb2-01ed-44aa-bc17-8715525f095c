---
apiVersion: v1
kind: Namespace
metadata:
  name: phoenix-app
  labels:
    name: phoenix-app
    environment: production
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: namespace
  annotations:
    description: "Dedicated namespace for Phoenix application and its dependencies"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: phoenix-app-quota
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: resource-quota
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "20"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: phoenix-app-limits
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: limit-range
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
