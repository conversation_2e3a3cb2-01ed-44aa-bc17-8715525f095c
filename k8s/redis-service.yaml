---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "ClusterIP service for Redis cache"
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  - name: metrics
    port: 9121
    targetPort: 9121
    protocol: TCP
  selector:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
  sessionAffinity: None
