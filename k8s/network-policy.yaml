---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: phoenix-app-network-policy
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: network-policy
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Network policy for Phoenix application security"
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: phoenix-app
      app.kubernetes.io/component: web-server
  
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress rules - what can connect TO Phoenix app
  ingress:
  # Allow ingress from Azure Application Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - podSelector:
        matchLabels:
          app: ingress-azure
    ports:
    - protocol: TCP
      port: 4000
  
  # Allow health checks from within the cluster
  - from:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 4000
    - protocol: TCP
      port: 9090  # Metrics
  
  # Allow communication from other Phoenix app pods (for clustering)
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-app
    ports:
    - protocol: TCP
      port: 4000
    - protocol: TCP
      port: 4369  # EPMD (Erlang Port Mapper Daemon)
    - protocol: TCP
      port: 9100-9200  # Erlang distribution ports
  
  # Egress rules - what Phoenix app can connect TO
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow connection to PostgreSQL
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: postgres
          app.kubernetes.io/component: database
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow connection to Redis
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: redis
          app.kubernetes.io/component: cache
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow HTTPS outbound for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Allow NTP for time synchronization
  - to: []
    ports:
    - protocol: UDP
      port: 123
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: postgres-network-policy
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-network-policy
    app.kubernetes.io/part-of: phoenix-application
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
  
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress rules - what can connect TO PostgreSQL
  ingress:
  # Allow connections from Phoenix app
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-app
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow connections from scheduler jobs
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-scheduler
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow connections from cleanup jobs
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-cleanup
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow monitoring/metrics collection
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9187  # PostgreSQL exporter
  
  # Egress rules - what PostgreSQL can connect TO
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow NTP for time synchronization
  - to: []
    ports:
    - protocol: UDP
      port: 123
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: redis-network-policy
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-network-policy
    app.kubernetes.io/part-of: phoenix-application
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: cache
  
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress rules - what can connect TO Redis
  ingress:
  # Allow connections from Phoenix app
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-app
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow connections from scheduler jobs
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: phoenix-scheduler
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow monitoring/metrics collection
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9121  # Redis exporter
  
  # Egress rules - what Redis can connect TO
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow NTP for time synchronization
  - to: []
    ports:
    - protocol: UDP
      port: 123
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: default-deny-policy
  annotations:
    description: "Default deny all traffic policy - explicit allow rules override this"
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
