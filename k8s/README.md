# Phoenix Elixir Application - Kubernetes Configuration

This directory contains comprehensive Kubernetes deployment configurations for a Phoenix Elixir application designed to run on Azure Kubernetes Service (AKS).

## 📁 File Structure

```
k8s/
├── README.md                    # This file
├── deployment-guide.md          # Detailed deployment instructions
├── kustomization.yaml          # Kustomize configuration
│
├── namespace.yaml              # Namespace and resource quotas
├── rbac.yaml                   # RBAC configuration
├── azure-storage.yaml          # Azure storage classes and volumes
│
├── phoenix-configmap.yaml      # Application configuration
├── phoenix-secret.yaml         # Sensitive configuration
│
├── postgres-deployment.yaml    # PostgreSQL database
├── postgres-service.yaml       # PostgreSQL service
│
├── redis-deployment.yaml       # Redis cache
├── redis-service.yaml          # Redis service
│
├── phoenix-deployment.yaml     # Phoenix application
├── phoenix-service.yaml        # Phoenix service
│
├── phoenix-hpa.yaml           # Horizontal Pod Autoscaler
├── phoenix-scheduler.yaml      # CronJobs for background tasks
├── pdb.yaml                    # Pod Disruption Budget
│
├── phoenix-ingress.yaml        # Ingress with Azure Application Gateway
├── network-policy.yaml         # Network security policies
│
└── monitoring.yaml             # Monitoring and alerting configuration
```

## 🚀 Quick Start

### Prerequisites
- Azure Kubernetes Service (AKS) cluster
- `kubectl` configured to connect to your cluster
- Container image built and pushed to Azure Container Registry

### Basic Deployment

1. **Clone and configure**:
   ```bash
   git clone <your-repo>
   cd k8s/
   ```

2. **Update configuration placeholders** in all YAML files:
   - `{{PHOENIX_IMAGE}}` - Your Phoenix container image
   - `{{DOMAIN_NAME}}` - Your application domain
   - `{{DATABASE_URL_BASE64}}` - Base64 encoded database URL
   - `{{SECRET_KEY_BASE_BASE64}}` - Base64 encoded Phoenix secret
   - And other placeholders as documented

3. **Deploy in order**:
   ```bash
   # Infrastructure
   kubectl apply -f namespace.yaml
   kubectl apply -f rbac.yaml
   kubectl apply -f azure-storage.yaml
   
   # Configuration
   kubectl apply -f phoenix-configmap.yaml
   kubectl apply -f phoenix-secret.yaml
   
   # Database and Cache
   kubectl apply -f postgres-deployment.yaml
   kubectl apply -f postgres-service.yaml
   kubectl apply -f redis-deployment.yaml
   kubectl apply -f redis-service.yaml
   
   # Application
   kubectl apply -f phoenix-deployment.yaml
   kubectl apply -f phoenix-service.yaml
   
   # Scaling and Jobs
   kubectl apply -f phoenix-hpa.yaml
   kubectl apply -f phoenix-scheduler.yaml
   kubectl apply -f pdb.yaml
   
   # Networking
   kubectl apply -f network-policy.yaml
   kubectl apply -f phoenix-ingress.yaml
   
   # Monitoring
   kubectl apply -f monitoring.yaml
   ```

4. **Verify deployment**:
   ```bash
   kubectl get all -n phoenix-app
   kubectl get ingress -n phoenix-app
   ```

### Using Kustomize (Recommended)

```bash
# Deploy using kustomize
kubectl apply -k .

# Or with specific overlay
kubectl apply -k overlays/production/
```

## 🏗️ Architecture Overview

### Components

- **Phoenix Application**: Elixir/Phoenix web application (3+ replicas)
- **PostgreSQL**: Primary database with persistent storage
- **Redis**: Cache layer for Nebulex caching
- **Azure Application Gateway**: Ingress controller with TLS termination
- **Horizontal Pod Autoscaler**: Auto-scaling based on CPU/memory
- **CronJobs**: Background task scheduling
- **Network Policies**: Security and traffic control

### Resource Allocation

| Component | CPU Request | CPU Limit | Memory Request | Memory Limit |
|-----------|-------------|-----------|----------------|--------------|
| Phoenix   | 500m        | 1000m     | 1Gi            | 2Gi          |
| PostgreSQL| 250m        | 500m      | 512Mi          | 1Gi          |
| Redis     | 100m        | 200m      | 256Mi          | 512Mi        |

### Storage

- **PostgreSQL**: 32Gi Premium SSD (`managed-premium`)
- **Redis**: 8Gi Premium SSD for cache persistence
- **Shared Storage**: 100Gi Azure Files for shared assets (optional)
- **Blob Storage**: 1000Gi Azure Blob for file uploads (optional)

## 🔧 Configuration

### Environment Variables

Key configuration is managed through ConfigMaps and Secrets:

**ConfigMap** (`phoenix-configmap.yaml`):
- Application settings (PHX_HOST, PORT, MIX_ENV)
- Database connection parameters
- Cache configuration
- Feature flags

**Secret** (`phoenix-secret.yaml`):
- Database credentials
- Phoenix secret keys
- API keys and tokens
- TLS certificates

### Placeholder Replacement

Before deployment, replace these placeholders:

#### Application
- `{{PHOENIX_IMAGE}}` - Container image URL
- `{{DOMAIN_NAME}}` - Application domain

#### Database
- `{{DATABASE_URL_BASE64}}` - Full database connection string
- `{{DB_USERNAME_BASE64}}` - Database username
- `{{DB_PASSWORD_BASE64}}` - Database password

#### Security
- `{{SECRET_KEY_BASE_BASE64}}` - Phoenix secret key base
- `{{LIVE_VIEW_SIGNING_SALT_BASE64}}` - LiveView signing salt
- `{{GUARDIAN_SECRET_KEY_BASE64}}` - Guardian authentication key
- `{{JWT_SECRET_BASE64}}` - JWT signing secret
- `{{ENCRYPTION_KEY_BASE64}}` - Data encryption key

#### Azure
- `{{AZURE_SUBSCRIPTION_ID}}` - Azure subscription
- `{{AZURE_RESOURCE_GROUP}}` - Resource group name
- `{{AZURE_CLIENT_ID}}` - Service principal client ID
- `{{AZURE_TENANT_ID}}` - Azure AD tenant ID
- `{{AZURE_STORAGE_ACCOUNT}}` - Storage account name

## 🔒 Security Features

### Network Security
- **Network Policies**: Restrict inter-pod communication
- **Pod Security Standards**: Enforce security contexts
- **RBAC**: Principle of least privilege access

### Data Security
- **Secrets Management**: Kubernetes secrets for sensitive data
- **TLS Encryption**: End-to-end encryption with Let's Encrypt
- **Azure Key Vault**: Integration for advanced secret management

### Application Security
- **Non-root Containers**: All containers run as non-root users
- **Read-only Root Filesystem**: Where applicable
- **Security Contexts**: Drop unnecessary capabilities

## 📊 Monitoring and Observability

### Metrics Collection
- **Prometheus**: Metrics scraping from all components
- **Grafana Dashboards**: Pre-configured dashboards
- **Azure Monitor**: Integration with Azure monitoring

### Alerting Rules
- High error rates
- High response times
- Resource exhaustion
- Pod restart frequency
- Database connection issues

### Health Checks
- **Liveness Probes**: Detect and restart unhealthy containers
- **Readiness Probes**: Control traffic routing
- **Startup Probes**: Handle slow-starting applications

## 🔄 Scaling and High Availability

### Horizontal Pod Autoscaler
- **Min Replicas**: 3 (high availability)
- **Max Replicas**: 10 (cost control)
- **CPU Target**: 70% utilization
- **Memory Target**: 80% utilization

### Pod Disruption Budget
- **Min Available**: 2 pods during disruptions
- **Graceful Shutdowns**: 30-second termination grace period

### Database High Availability
- **StatefulSet**: Ordered deployment and scaling
- **Persistent Storage**: Data survives pod restarts
- **Backup Strategy**: Regular automated backups

## 🔧 Maintenance

### Regular Tasks
1. **Update Images**: Keep container images current
2. **Monitor Resources**: Watch for resource constraints
3. **Certificate Renewal**: Automated via cert-manager
4. **Database Maintenance**: Regular VACUUM and ANALYZE
5. **Security Updates**: Apply patches promptly

### Backup and Recovery
```bash
# Database backup
kubectl exec -it deployment/postgres -n phoenix-app -- \
  pg_dump -U postgres phoenix_prod > backup-$(date +%Y%m%d).sql

# Application logs
kubectl logs deployment/phoenix-app -n phoenix-app --since=24h > app-logs.txt
```

## 🚨 Troubleshooting

### Common Issues

1. **Pod Won't Start**
   ```bash
   kubectl describe pod <pod-name> -n phoenix-app
   kubectl logs <pod-name> -n phoenix-app
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it deployment/phoenix-app -n phoenix-app -- \
     /app/bin/phoenix_app eval "Phoenix.App.Repo.query!(\"SELECT 1\")"
   ```

3. **Ingress Not Working**
   ```bash
   kubectl describe ingress phoenix-ingress -n phoenix-app
   kubectl get events -n phoenix-app --sort-by='.lastTimestamp'
   ```

### Debug Commands
```bash
# Check all resources
kubectl get all -n phoenix-app

# Check events
kubectl get events -n phoenix-app --sort-by='.lastTimestamp'

# Check resource usage
kubectl top pods -n phoenix-app
kubectl top nodes

# Port forward for local testing
kubectl port-forward service/phoenix-service 8080:80 -n phoenix-app
```

## 📚 Additional Resources

- [Deployment Guide](deployment-guide.md) - Detailed step-by-step instructions
- [Phoenix Documentation](https://hexdocs.pm/phoenix/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Azure Kubernetes Service](https://docs.microsoft.com/en-us/azure/aks/)

## 🤝 Contributing

When modifying these configurations:

1. Test changes in a development environment first
2. Update documentation for any new features
3. Follow Kubernetes best practices
4. Validate YAML syntax before committing
5. Update version tags and labels appropriately

## 📄 License

This configuration is provided as-is for educational and production use. Modify according to your specific requirements and security policies.
