---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: phoenix-ingress
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Ingress for Phoenix application with Azure Application Gateway"
    
    # Azure Application Gateway Ingress Controller annotations
    kubernetes.io/ingress.class: "azure/application-gateway"
    appgw.ingress.kubernetes.io/backend-protocol: "http"
    appgw.ingress.kubernetes.io/connection-draining: "true"
    appgw.ingress.kubernetes.io/connection-draining-timeout: "30"
    appgw.ingress.kubernetes.io/cookie-based-affinity: "false"
    appgw.ingress.kubernetes.io/request-timeout: "30"
    appgw.ingress.kubernetes.io/backend-path-prefix: "/"
    
    # SSL/TLS configuration
    appgw.ingress.kubernetes.io/ssl-redirect: "true"
    appgw.ingress.kubernetes.io/use-private-ip: "false"
    
    # Health probe configuration
    appgw.ingress.kubernetes.io/health-probe-hostname: "{{DOMAIN_NAME}}"
    appgw.ingress.kubernetes.io/health-probe-path: "/health"
    appgw.ingress.kubernetes.io/health-probe-port: "80"
    appgw.ingress.kubernetes.io/health-probe-protocol: "Http"
    appgw.ingress.kubernetes.io/health-probe-interval: "30"
    appgw.ingress.kubernetes.io/health-probe-timeout: "5"
    appgw.ingress.kubernetes.io/health-probe-unhealthy-threshold: "3"
    
    # WAF (Web Application Firewall) configuration
    appgw.ingress.kubernetes.io/waf-policy-for-path: "/subscriptions/{{AZURE_SUBSCRIPTION_ID}}/resourceGroups/{{AZURE_RESOURCE_GROUP}}/providers/Microsoft.Network/ApplicationGatewayWebApplicationFirewallPolicies/{{WAF_POLICY_NAME}}"
    
    # Rate limiting
    appgw.ingress.kubernetes.io/rewrite-rule-set: "phoenix-rewrite-rules"
    
    # Certificate management with cert-manager
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
    
    # Additional Azure-specific annotations
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: "{{SSL_CERTIFICATE_NAME}}"
    appgw.ingress.kubernetes.io/hostname-extension: "{{DOMAIN_NAME}}"
spec:
  # TLS configuration
  tls:
  - hosts:
    - "{{DOMAIN_NAME}}"
    - "www.{{DOMAIN_NAME}}"
    secretName: phoenix-tls-secret
  
  # Ingress rules
  rules:
  # Main domain
  - host: "{{DOMAIN_NAME}}"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
      
      # Health check endpoint
      - path: /health
        pathType: Exact
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
      
      # Metrics endpoint (protected)
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: phoenix-service
            port:
              number: 9090
  
  # WWW subdomain redirect
  - host: "www.{{DOMAIN_NAME}}"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
---
apiVersion: v1
kind: Secret
metadata:
  name: phoenix-tls-secret
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: tls-certificate
  annotations:
    description: "TLS certificate for Phoenix application"
    cert-manager.io/issuer: "letsencrypt-prod"
type: kubernetes.io/tls
data:
  # These will be populated by cert-manager
  tls.crt: ""
  tls.key: ""
---
# Certificate resource for cert-manager
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: phoenix-certificate
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: certificate
spec:
  secretName: phoenix-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - "{{DOMAIN_NAME}}"
  - "www.{{DOMAIN_NAME}}"
  
  # ACME challenge configuration
  acme:
    config:
    - http01:
        ingressClass: azure/application-gateway
      domains:
      - "{{DOMAIN_NAME}}"
      - "www.{{DOMAIN_NAME}}"
---
# ClusterIssuer for Let's Encrypt (should be applied cluster-wide)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/component: cluster-issuer
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: "{{LETSENCRYPT_EMAIL}}"
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    solvers:
    - http01:
        ingress:
          class: azure/application-gateway
          podTemplate:
            metadata:
              annotations:
                appgw.ingress.kubernetes.io/use-private-ip: "false"
    - dns01:
        azureDNS:
          clientID: "{{AZURE_CLIENT_ID}}"
          clientSecretSecretRef:
            name: azuredns-config
            key: client-secret
          subscriptionID: "{{AZURE_SUBSCRIPTION_ID}}"
          tenantID: "{{AZURE_TENANT_ID}}"
          resourceGroupName: "{{AZURE_DNS_RESOURCE_GROUP}}"
          hostedZoneName: "{{DOMAIN_NAME}}"
          environment: AzurePublicCloud
