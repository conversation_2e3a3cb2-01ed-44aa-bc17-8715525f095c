---
apiVersion: v1
kind: Service
metadata:
  name: phoenix-service
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "ClusterIP service for Phoenix web application"
    service.beta.kubernetes.io/azure-load-balancer-internal: "false"
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 4000
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-server
  sessionAffinity: None
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: phoenix-app-sa
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: service-account
  annotations:
    description: "Service account for Phoenix application with Azure integrations"
    # Azure Workload Identity annotation (if using Azure AD Workload Identity)
    azure.workload.identity/client-id: "{{AZURE_CLIENT_ID}}"
automountServiceAccountToken: true
