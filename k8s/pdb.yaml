---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: phoenix-app-pdb
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: pod-disruption-budget
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Pod Disruption Budget for Phoenix application high availability"
spec:
  # Ensure minimum 2 pods are always available during disruptions
  minAvailable: 2
  
  # Alternative: Use percentage instead of absolute number
  # minAvailable: 50%
  
  # Or specify maximum unavailable instead
  # maxUnavailable: 1
  
  selector:
    matchLabels:
      app.kubernetes.io/name: phoenix-app
      app.kubernetes.io/component: web-server
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: postgres-pdb
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-pod-disruption-budget
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Pod Disruption Budget for PostgreSQL database"
spec:
  # For single replica database, ensure it's not disrupted
  minAvailable: 1
  
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis-pdb
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-pod-disruption-budget
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Pod Disruption Budget for Redis cache"
spec:
  # For single replica cache, ensure it's not disrupted during critical operations
  minAvailable: 1
  
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: cache
