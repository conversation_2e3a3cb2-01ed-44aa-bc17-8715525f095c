---
apiVersion: v1
kind: ConfigMap
metadata:
  name: phoenix-config
  namespace: phoenix-app
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: configmap
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Non-sensitive configuration for Phoenix application"
data:
  # Phoenix application configuration
  PHX_HOST: "{{DOMAIN_NAME}}"
  PORT: "4000"
  MIX_ENV: "prod"
  
  # Database configuration (non-sensitive)
  DB_POOL_SIZE: "10"
  DB_TIMEOUT: "15000"
  DB_QUEUE_TARGET: "50"
  DB_QUEUE_INTERVAL: "1000"
  
  # Cache configuration
  CACHE_ADAPTER: "Nebulex.Adapters.Redis"
  CACHE_TTL: "3600"
  REDIS_HOST: "redis-service.phoenix-app.svc.cluster.local"
  REDIS_PORT: "6379"
  REDIS_DATABASE: "0"
  
  # Application settings
  LOG_LEVEL: "info"
  ENABLE_TELEMETRY: "true"
  HEALTH_CHECK_PATH: "/health"
  READY_CHECK_PATH: "/ready"
  
  # Phoenix LiveView configuration
  LIVE_VIEW_SIGNING_SALT: "phoenix_live_view"
  
  # Session configuration
  SESSION_TIMEOUT: "86400"
  
  # CORS configuration
  CORS_ORIGINS: "https://{{DOMAIN_NAME}}"
  
  # Rate limiting
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_MAX_REQUESTS: "1000"
  RATE_LIMIT_WINDOW_MS: "60000"
  
  # Background job configuration
  SCHEDULER_ENABLED: "true"
  
  # Monitoring and observability
  PROMETHEUS_METRICS_ENABLED: "true"
  METRICS_PORT: "9090"
  
  # Azure-specific configurations
  AZURE_STORAGE_ACCOUNT: "{{AZURE_STORAGE_ACCOUNT}}"
  AZURE_REGION: "{{AZURE_REGION}}"
