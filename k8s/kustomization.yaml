apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: phoenix-app-kustomization
  annotations:
    description: "Kustomization for Phoenix Elixir application on Azure Kubernetes Service"

# Namespace for all resources
namespace: phoenix-app

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/name: phoenix-app
  app.kubernetes.io/part-of: phoenix-application
  app.kubernetes.io/managed-by: kustomize
  environment: production

# Common annotations applied to all resources
commonAnnotations:
  deployment.kubernetes.io/managed-by: "kustomize"
  deployment.kubernetes.io/environment: "production"

# Resources to include in the deployment
resources:
  # Core infrastructure
  - namespace.yaml
  - rbac.yaml
  - azure-storage.yaml
  
  # Configuration
  - phoenix-configmap.yaml
  - phoenix-secret.yaml
  
  # Database layer
  - postgres-deployment.yaml
  - postgres-service.yaml
  
  # Cache layer
  - redis-deployment.yaml
  - redis-service.yaml
  
  # Application layer
  - phoenix-deployment.yaml
  - phoenix-service.yaml
  
  # Scaling and scheduling
  - phoenix-hpa.yaml
  - phoenix-scheduler.yaml
  - pdb.yaml
  
  # Networking and security
  - phoenix-ingress.yaml
  - network-policy.yaml
  
  # Monitoring
  - monitoring.yaml

# Images to be replaced (useful for CI/CD)
images:
  - name: "{{PHOENIX_IMAGE}}"
    newName: your-registry.azurecr.io/phoenix-app
    newTag: latest
  - name: postgres:14-alpine
    newName: postgres
    newTag: 14-alpine
  - name: redis:7-alpine
    newName: redis
    newTag: 7-alpine

# ConfigMap generator for environment-specific configuration
configMapGenerator:
  - name: phoenix-env-config
    literals:
      - ENVIRONMENT=production
      - CLUSTER_NAME=aks-phoenix-prod
      - AZURE_REGION=eastus
    options:
      disableNameSuffixHash: true

# Secret generator for sensitive configuration
secretGenerator:
  - name: phoenix-env-secrets
    literals:
      - DATABASE_PASSWORD=changeme
      - REDIS_PASSWORD=changeme
    type: Opaque
    options:
      disableNameSuffixHash: true

# Patches for environment-specific customizations
patches:
  # Patch for production resource limits
  - target:
      kind: Deployment
      name: phoenix-app
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "2000m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "4Gi"
  
  # Patch for production replica count
  - target:
      kind: Deployment
      name: phoenix-app
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 5

# Replacements for placeholder values
replacements:
  # Replace domain name across all resources
  - source:
      kind: ConfigMap
      name: phoenix-config
      fieldPath: data.PHX_HOST
    targets:
      - select:
          kind: Ingress
          name: phoenix-ingress
        fieldPaths:
          - spec.rules.0.host
          - spec.tls.0.hosts.0
  
  # Replace image references
  - source:
      kind: ConfigMap
      name: phoenix-env-config
      fieldPath: data.PHOENIX_IMAGE
    targets:
      - select:
          kind: Deployment
          name: phoenix-app
        fieldPaths:
          - spec.template.spec.containers.0.image
      - select:
          kind: CronJob
          name: phoenix-scheduler
        fieldPaths:
          - spec.jobTemplate.spec.template.spec.containers.0.image

# Generators for additional resources
generators:
  # Generate network policies based on labels
  - networkPolicyGenerator.yaml

# Transformers for additional customizations
transformers:
  # Add Azure-specific annotations
  - azureTransformer.yaml

# Validation rules
validators:
  - resourceValidator.yaml
