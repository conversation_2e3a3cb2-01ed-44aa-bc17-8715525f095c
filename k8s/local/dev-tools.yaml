---
# pgAdmin for database management
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgadmin
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: pgadmin
    app.kubernetes.io/component: database-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: pgadmin
  template:
    metadata:
      labels:
        app.kubernetes.io/name: pgadmin
        app.kubernetes.io/component: database-admin
    spec:
      containers:
      - name: pgadmin
        image: dpage/pgadmin4:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 50m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        
        env:
        - name: PGADMIN_DEFAULT_EMAIL
          value: "<EMAIL>"
        - name: PGADMIN_DEFAULT_PASSWORD
          value: "admin"
        - name: PGADMIN_CONFIG_SERVER_MODE
          value: "False"
        - name: PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED
          value: "False"
        
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        
        volumeMounts:
        - name: pgadmin-data
          mountPath: /var/lib/pgadmin
      
      volumes:
      - name: pgadmin-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: pgadmin-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: pgadmin
    app.kubernetes.io/component: database-admin-service
spec:
  type: NodePort
  ports:
  - name: http
    port: 80
    targetPort: 80
    nodePort: 30001
    protocol: TCP
  selector:
    app.kubernetes.io/name: pgadmin
---
# Redis Commander for Redis management
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-commander
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: redis-commander
    app.kubernetes.io/component: cache-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: redis-commander
  template:
    metadata:
      labels:
        app.kubernetes.io/name: redis-commander
        app.kubernetes.io/component: cache-admin
    spec:
      containers:
      - name: redis-commander
        image: rediscommander/redis-commander:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        
        env:
        - name: REDIS_HOSTS
          value: "local:redis-service.phoenix-app-local.svc.cluster.local:6379"
        - name: HTTP_USER
          value: "admin"
        - name: HTTP_PASSWORD
          value: "admin"
        
        ports:
        - name: http
          containerPort: 8081
          protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: redis-commander-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: redis-commander
    app.kubernetes.io/component: cache-admin-service
spec:
  type: NodePort
  ports:
  - name: http
    port: 8081
    targetPort: 8081
    nodePort: 30002
    protocol: TCP
  selector:
    app.kubernetes.io/name: redis-commander
---
# Mailhog for email testing
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mailhog
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: mailhog
    app.kubernetes.io/component: email-testing
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: mailhog
  template:
    metadata:
      labels:
        app.kubernetes.io/name: mailhog
        app.kubernetes.io/component: email-testing
    spec:
      containers:
      - name: mailhog
        image: mailhog/mailhog:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
        
        ports:
        - name: smtp
          containerPort: 1025
          protocol: TCP
        - name: http
          containerPort: 8025
          protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: mailhog-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: mailhog
    app.kubernetes.io/component: email-testing-service
spec:
  type: NodePort
  ports:
  - name: smtp
    port: 1025
    targetPort: 1025
    nodePort: 30025
    protocol: TCP
  - name: http
    port: 8025
    targetPort: 8025
    nodePort: 30003
    protocol: TCP
  selector:
    app.kubernetes.io/name: mailhog
