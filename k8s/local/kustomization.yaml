apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: phoenix-app-local-kustomization
  annotations:
    description: "Kustomization for Phoenix Elixir application local development"

# Namespace for all resources
namespace: phoenix-app-local

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/name: phoenix-app
  app.kubernetes.io/part-of: phoenix-application
  app.kubernetes.io/managed-by: kustomize
  environment: development

# Common annotations applied to all resources
commonAnnotations:
  deployment.kubernetes.io/managed-by: "kustomize"
  deployment.kubernetes.io/environment: "development"

# Resources to include in the deployment
resources:
  # Core infrastructure
  - namespace.yaml
  - local-storage.yaml
  
  # Configuration
  - phoenix-configmap.yaml
  - phoenix-secret.yaml
  
  # Database layer
  - postgres-deployment.yaml
  - postgres-service.yaml
  
  # Cache layer
  - redis-deployment.yaml
  - redis-service.yaml
  
  # Application layer
  - phoenix-deployment.yaml
  - phoenix-service.yaml
  
  # Networking
  - local-ingress.yaml
  
  # Development tools (optional)
  - dev-tools.yaml
  
  # Monitoring (optional)
  - monitoring-local.yaml

# Images to be replaced
images:
  - name: "your-phoenix-image:latest"
    newName: phoenix-app
    newTag: local
  - name: postgres:14-alpine
    newName: postgres
    newTag: 14-alpine
  - name: redis:7-alpine
    newName: redis
    newTag: 7-alpine

# ConfigMap generator for environment-specific configuration
configMapGenerator:
  - name: phoenix-local-env-config
    literals:
      - ENVIRONMENT=development
      - CLUSTER_TYPE=local
      - DEBUG_MODE=true
    options:
      disableNameSuffixHash: true

# Patches for local development customizations
patches:
  # Patch for development resource limits (lower)
  - target:
      kind: Deployment
      name: phoenix-app
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: "1000m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: "1Gi"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: "200m"
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: "512Mi"
  
  # Patch for single replica (development)
  - target:
      kind: Deployment
      name: phoenix-app
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
  
  # Patch for development image pull policy
  - target:
      kind: Deployment
      name: phoenix-app
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/imagePullPolicy
        value: "Never"
      - op: replace
        path: /spec/template/spec/initContainers/0/imagePullPolicy
        value: "Never"

# Replacements for placeholder values
replacements:
  # Replace domain name for local development
  - source:
      kind: ConfigMap
      name: phoenix-config
      fieldPath: data.PHX_HOST
    targets:
      - select:
          kind: Ingress
          name: phoenix-ingress
        fieldPaths:
          - spec.rules.0.host
  
  # Replace image references
  - source:
      kind: ConfigMap
      name: phoenix-local-env-config
      fieldPath: data.PHOENIX_IMAGE
    targets:
      - select:
          kind: Deployment
          name: phoenix-app
        fieldPaths:
          - spec.template.spec.containers.0.image
        options:
          create: true
