# Phoenix Elixir Application - Local Kubernetes Development

This directory contains simplified Kubernetes configurations for running the Phoenix application locally using minikube, kind, or Docker Desktop Kubernetes.

## 🚀 Quick Start

### Prerequisites

Choose one of these local Kubernetes options:

#### Option 1: Docker Desktop (Recommended for beginners)
```bash
# Enable Kubernetes in Docker Desktop settings
# No additional setup required
```

#### Option 2: Minikube
```bash
# Install minikube
brew install minikube  # macOS
# or follow: https://minikube.sigs.k8s.io/docs/start/

# Start minikube
minikube start --memory=4096 --cpus=2

# Enable ingress addon
minikube addons enable ingress
```

#### Option 3: kind (Kubernetes in Docker)
```bash
# Install kind
brew install kind  # macOS
# or follow: https://kind.sigs.k8s.io/docs/user/quick-start/

# Create cluster
kind create cluster --config=kind-config.yaml
```

### Build Your Phoenix Application

1. **Create a Dockerfile** in your Phoenix project root:
```dockerfile
FROM elixir:1.15-alpine AS build

# Install build dependencies
RUN apk add --no-cache build-base npm git python3

# Prepare build dir
WORKDIR /app

# Install hex + rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Set build ENV
ENV MIX_ENV=prod

# Install mix dependencies
COPY mix.exs mix.lock ./
COPY config config
RUN mix do deps.get, deps.compile

# Build assets
COPY assets/package.json assets/package-lock.json ./assets/
RUN npm --prefix ./assets ci --progress=false --no-audit --loglevel=error

COPY priv priv
COPY assets assets
RUN npm run --prefix ./assets deploy
RUN mix phx.digest

# Compile and build release
COPY lib lib
RUN mix do compile, release

# Prepare release image
FROM alpine:3.18 AS app
RUN apk add --no-cache openssl ncurses-libs

WORKDIR /app

RUN chown nobody:nobody /app

USER nobody:nobody

COPY --from=build --chown=nobody:nobody /app/_build/prod/rel/phoenix_app ./

ENV HOME=/app

CMD ["bin/phoenix_app", "start"]
```

2. **Build the image**:
```bash
# Build your Phoenix application image
docker build -t phoenix-app:local .

# For minikube, load the image
minikube image load phoenix-app:local

# For kind, load the image
kind load docker-image phoenix-app:local
```

### Deploy to Local Kubernetes

1. **Update the image reference** in `phoenix-deployment.yaml`:
```yaml
# Change this line:
image: "your-phoenix-image:latest"
# To:
image: "phoenix-app:local"
```

2. **Deploy the application**:
```bash
# Navigate to the local config directory
cd k8s/local/

# Deploy in order
kubectl apply -f namespace.yaml
kubectl apply -f phoenix-configmap.yaml
kubectl apply -f phoenix-secret.yaml

# Deploy database
kubectl apply -f postgres-deployment.yaml
kubectl apply -f postgres-service.yaml

# Wait for database to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n phoenix-app-local --timeout=300s

# Deploy cache
kubectl apply -f redis-deployment.yaml
kubectl apply -f redis-service.yaml

# Wait for Redis to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n phoenix-app-local --timeout=300s

# Deploy Phoenix application
kubectl apply -f phoenix-deployment.yaml
kubectl apply -f phoenix-service.yaml

# Optional: Deploy ingress
kubectl apply -f local-ingress.yaml
```

3. **Access your application**:

#### Option A: NodePort (Direct access)
```bash
# Application will be available at:
# http://localhost:30000

# If using minikube:
minikube service phoenix-service -n phoenix-app-local --url
```

#### Option B: Port Forward
```bash
# Forward local port to service
kubectl port-forward service/phoenix-service 4000:80 -n phoenix-app-local

# Access at: http://localhost:4000
```

#### Option C: Ingress (if configured)
```bash
# Add to /etc/hosts (macOS/Linux) or C:\Windows\System32\drivers\etc\hosts (Windows)
echo "127.0.0.1 phoenix.local" | sudo tee -a /etc/hosts

# For minikube, get the IP:
minikube ip
# Then add: <minikube-ip> phoenix.local

# Access at: http://phoenix.local
```

## 🔧 Configuration

### Environment Variables

The local configuration uses development-friendly settings:

- **Database**: `postgres:postgres@postgres-service:5432/phoenix_dev`
- **Redis**: No password, connects to `redis-service:6379`
- **Phoenix**: Runs in `dev` mode with debug logging
- **CORS**: Allows `http://localhost:4000`

### Resource Limits

Reduced for local development:

| Component | CPU Request | CPU Limit | Memory Request | Memory Limit |
|-----------|-------------|-----------|----------------|--------------|
| Phoenix   | 200m        | 1000m     | 512Mi          | 1Gi          |
| PostgreSQL| 100m        | 500m      | 256Mi          | 512Mi        |
| Redis     | 50m         | 200m      | 128Mi          | 256Mi        |

### Storage

- **PostgreSQL**: 5Gi local storage
- **Redis**: 1Gi local storage

## 🐛 Troubleshooting

### Check Pod Status
```bash
kubectl get pods -n phoenix-app-local
kubectl describe pod <pod-name> -n phoenix-app-local
kubectl logs <pod-name> -n phoenix-app-local
```

### Database Issues
```bash
# Connect to database
kubectl exec -it deployment/postgres -n phoenix-app-local -- psql -U postgres -d phoenix_dev

# Check database logs
kubectl logs deployment/postgres -n phoenix-app-local
```

### Application Issues
```bash
# Check Phoenix logs
kubectl logs deployment/phoenix-app -n phoenix-app-local -f

# Execute commands in Phoenix container
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "System.version()"
```

### Network Issues
```bash
# Test service connectivity
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- nslookup postgres-service.phoenix-app-local.svc.cluster.local

# Port forward for debugging
kubectl port-forward deployment/postgres 5432:5432 -n phoenix-app-local
kubectl port-forward deployment/redis 6379:6379 -n phoenix-app-local
```

## 🔄 Development Workflow

### Update Application
```bash
# Rebuild image
docker build -t phoenix-app:local .

# Load into cluster (minikube/kind)
minikube image load phoenix-app:local
# or
kind load docker-image phoenix-app:local

# Restart deployment
kubectl rollout restart deployment/phoenix-app -n phoenix-app-local
```

### Database Operations
```bash
# Run migrations
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "Phoenix.App.Release.migrate()"

# Seed database
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "Phoenix.App.Release.seed()"

# Reset database
kubectl exec -it deployment/postgres -n phoenix-app-local -- psql -U postgres -c "DROP DATABASE IF EXISTS phoenix_dev; CREATE DATABASE phoenix_dev;"
```

### Clean Up
```bash
# Delete all resources
kubectl delete namespace phoenix-app-local

# Stop minikube
minikube stop

# Delete kind cluster
kind delete cluster
```

## 📝 Notes

- This configuration is optimized for local development
- Security is relaxed (plain text secrets, no network policies)
- Resource limits are reduced for local machines
- Single replicas for all services
- No monitoring or advanced features

For production deployment, use the main Azure configuration in the parent directory.

## 🔗 Useful Commands

```bash
# Watch all pods
kubectl get pods -n phoenix-app-local -w

# Get all resources
kubectl get all -n phoenix-app-local

# Describe ingress
kubectl describe ingress -n phoenix-app-local

# Check events
kubectl get events -n phoenix-app-local --sort-by='.lastTimestamp'
```
