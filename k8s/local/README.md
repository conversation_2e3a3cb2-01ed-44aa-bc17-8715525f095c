# Phoenix Elixir Application - Local Kubernetes Development

This directory contains a complete local development environment for the Phoenix Elixir application, optimized for developer laptops and workstations. It provides simplified Kubernetes configurations that work with Docker Desktop, minikube, and kind, plus additional development tools and automation scripts.

## 📁 Complete File Structure

```
k8s/local/
├── README.md                    # This comprehensive guide
├── kustomization.yaml          # Kustomize configuration for local dev
├── Dockerfile.example          # Example Dockerfile for Phoenix app
│
├── namespace.yaml              # Local namespace with reduced quotas
├── local-storage.yaml          # Local storage classes and PVs
│
├── phoenix-configmap.yaml      # Development-friendly configuration
├── phoenix-secret.yaml         # Plain text secrets for easy debugging
│
├── postgres-deployment.yaml    # PostgreSQL with reduced resources
├── postgres-service.yaml       # PostgreSQL service
│
├── redis-deployment.yaml       # Redis cache with persistence
├── redis-service.yaml          # Redis service
│
├── phoenix-deployment.yaml     # Phoenix app (single replica)
├── phoenix-service.yaml        # Phoenix service with NodePort
│
├── local-ingress.yaml          # Simple NGINX ingress
├── dev-tools.yaml              # pg<PERSON><PERSON><PERSON>, <PERSON>is Commander, Mailhog
├── monitoring-local.yaml       # Prometheus & Grafana for local dev
│
├── docker-compose.yml          # Alternative Docker Compose setup
├── kind-config.yaml            # kind cluster configuration
│
└── scripts/
    ├── setup-local.sh          # Automated setup script
    ├── cleanup-local.sh        # Complete cleanup script
    └── dev-helpers.sh          # Development helper commands
```

## 🎯 Key Features

### **Optimized for Local Development:**
- **Reduced Resource Requirements**: Suitable for developer laptops
- **Single Replica Deployments**: Conserve CPU and memory
- **Development Environment Variables**: `MIX_ENV=dev`, debug logging
- **Plain Text Secrets**: Easy debugging without base64 encoding
- **Fast Iteration**: Quick rebuilds and deployments

### **Multiple Kubernetes Options:**
- **Docker Desktop Kubernetes**: Zero-config option
- **Minikube**: Full-featured local cluster
- **Kind**: Lightweight Kubernetes in Docker
- **Docker Compose**: Alternative for simpler setups

### **Comprehensive Development Tools:**
- **pgAdmin**: Database administration interface
- **Redis Commander**: Redis management interface
- **Mailhog**: Email testing and debugging
- **Prometheus**: Metrics collection
- **Grafana**: Monitoring dashboards

### **Automation & Helper Scripts:**
- **One-command setup**: Automated environment deployment
- **Development helpers**: Logs, shell access, database operations
- **Easy cleanup**: Complete environment teardown

## 🚀 Quick Start

### Prerequisites

Choose one of these local Kubernetes options:

#### Option 1: Docker Desktop (Recommended for beginners)
```bash
# Enable Kubernetes in Docker Desktop settings
# No additional setup required
```

#### Option 2: Minikube
```bash
# Install minikube
brew install minikube  # macOS
# or follow: https://minikube.sigs.k8s.io/docs/start/

# Start minikube
minikube start --memory=4096 --cpus=2

# Enable ingress addon
minikube addons enable ingress
```

#### Option 3: kind (Kubernetes in Docker)
```bash
# Install kind
brew install kind  # macOS
# or follow: https://kind.sigs.k8s.io/docs/user/quick-start/

# Create cluster
kind create cluster --config=kind-config.yaml
```

### Build Your Phoenix Application

1. **Create a Dockerfile** in your Phoenix project root:
```dockerfile
FROM elixir:1.15-alpine AS build

# Install build dependencies
RUN apk add --no-cache build-base npm git python3

# Prepare build dir
WORKDIR /app

# Install hex + rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Set build ENV
ENV MIX_ENV=prod

# Install mix dependencies
COPY mix.exs mix.lock ./
COPY config config
RUN mix do deps.get, deps.compile

# Build assets
COPY assets/package.json assets/package-lock.json ./assets/
RUN npm --prefix ./assets ci --progress=false --no-audit --loglevel=error

COPY priv priv
COPY assets assets
RUN npm run --prefix ./assets deploy
RUN mix phx.digest

# Compile and build release
COPY lib lib
RUN mix do compile, release

# Prepare release image
FROM alpine:3.18 AS app
RUN apk add --no-cache openssl ncurses-libs

WORKDIR /app

RUN chown nobody:nobody /app

USER nobody:nobody

COPY --from=build --chown=nobody:nobody /app/_build/prod/rel/phoenix_app ./

ENV HOME=/app

CMD ["bin/phoenix_app", "start"]
```

2. **Build the image**:
```bash
# Build your Phoenix application image
docker build -t phoenix-app:local .

# For minikube, load the image
minikube image load phoenix-app:local

# For kind, load the image
kind load docker-image phoenix-app:local
```

### Deploy to Local Kubernetes

1. **Update the image reference** in `phoenix-deployment.yaml`:
```yaml
# Change this line:
image: "your-phoenix-image:latest"
# To:
image: "phoenix-app:local"
```

2. **Deploy the application**:
```bash
# Navigate to the local config directory
cd k8s/local/

# Deploy in order
kubectl apply -f namespace.yaml
kubectl apply -f phoenix-configmap.yaml
kubectl apply -f phoenix-secret.yaml

# Deploy database
kubectl apply -f postgres-deployment.yaml
kubectl apply -f postgres-service.yaml

# Wait for database to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n phoenix-app-local --timeout=300s

# Deploy cache
kubectl apply -f redis-deployment.yaml
kubectl apply -f redis-service.yaml

# Wait for Redis to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n phoenix-app-local --timeout=300s

# Deploy Phoenix application
kubectl apply -f phoenix-deployment.yaml
kubectl apply -f phoenix-service.yaml

# Optional: Deploy development tools
kubectl apply -f dev-tools.yaml

# Optional: Deploy monitoring
kubectl apply -f monitoring-local.yaml

# Optional: Deploy ingress
kubectl apply -f local-ingress.yaml
```

### **Option 4: Automated Setup (Recommended)**

The easiest way to get started:

```bash
# Navigate to local directory
cd k8s/local/

# Run automated setup (builds image and deploys everything)
./scripts/setup-local.sh

# Or skip image build if you already have it
./scripts/setup-local.sh --skip-build

# Or minimal setup (no dev tools or monitoring)
./scripts/setup-local.sh --minimal
```

### **Option 5: Using Kustomize**

```bash
# Deploy using kustomize
kubectl apply -k .

# Check status
kubectl get all -n phoenix-app-local
```

3. **Access your application**:

#### Option A: NodePort (Direct access)
```bash
# Application will be available at:
# http://localhost:30000

# If using minikube:
minikube service phoenix-service -n phoenix-app-local --url
```

#### Option B: Port Forward
```bash
# Forward local port to service
kubectl port-forward service/phoenix-service 4000:80 -n phoenix-app-local

# Access at: http://localhost:4000
```

#### Option C: Ingress (if configured)
```bash
# Add to /etc/hosts (macOS/Linux) or C:\Windows\System32\drivers\etc\hosts (Windows)
echo "127.0.0.1 phoenix.local" | sudo tee -a /etc/hosts

# For minikube, get the IP:
minikube ip
# Then add: <minikube-ip> phoenix.local

# Access at: http://phoenix.local
```

## 🔧 Configuration

### Environment Variables

The local configuration uses development-friendly settings:

- **Database**: `postgres:postgres@postgres-service:5432/phoenix_dev`
- **Redis**: No password, connects to `redis-service:6379`
- **Phoenix**: Runs in `dev` mode with debug logging
- **CORS**: Allows `http://localhost:4000`

### Resource Limits

Reduced for local development:

| Component | CPU Request | CPU Limit | Memory Request | Memory Limit |
|-----------|-------------|-----------|----------------|--------------|
| Phoenix   | 200m        | 1000m     | 512Mi          | 1Gi          |
| PostgreSQL| 100m        | 500m      | 256Mi          | 512Mi        |
| Redis     | 50m         | 200m      | 128Mi          | 256Mi        |

### Storage

- **PostgreSQL**: 5Gi local storage
- **Redis**: 1Gi local storage

## 🐛 Troubleshooting

### Check Pod Status
```bash
kubectl get pods -n phoenix-app-local
kubectl describe pod <pod-name> -n phoenix-app-local
kubectl logs <pod-name> -n phoenix-app-local
```

### Database Issues
```bash
# Connect to database
kubectl exec -it deployment/postgres -n phoenix-app-local -- psql -U postgres -d phoenix_dev

# Check database logs
kubectl logs deployment/postgres -n phoenix-app-local
```

### Application Issues
```bash
# Check Phoenix logs
kubectl logs deployment/phoenix-app -n phoenix-app-local -f

# Execute commands in Phoenix container
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "System.version()"
```

### Network Issues
```bash
# Test service connectivity
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- nslookup postgres-service.phoenix-app-local.svc.cluster.local

# Port forward for debugging
kubectl port-forward deployment/postgres 5432:5432 -n phoenix-app-local
kubectl port-forward deployment/redis 6379:6379 -n phoenix-app-local
```

## 🛠️ Development Tools & Access

### **Access URLs**

Once deployed, access your services at:

| Service | URL | Credentials |
|---------|-----|-------------|
| **Phoenix App** | http://localhost:30000 | - |
| **pgAdmin** | http://localhost:30001 | <EMAIL> / admin |
| **Redis Commander** | http://localhost:30002 | admin / admin |
| **Mailhog** | http://localhost:30003 | - |
| **Prometheus** | http://localhost:30004 | - |
| **Grafana** | http://localhost:30005 | admin / admin |

*For minikube, replace `localhost` with `$(minikube ip)`*

### **Helper Scripts**

Use the development helper script for common tasks:

```bash
# Show environment status
./scripts/dev-helpers.sh status

# View application logs
./scripts/dev-helpers.sh logs phoenix

# Open shell in Phoenix container
./scripts/dev-helpers.sh shell phoenix

# Connect to PostgreSQL
./scripts/dev-helpers.sh db connect

# Run database migrations
./scripts/dev-helpers.sh db migrate

# Restart Phoenix application
./scripts/dev-helpers.sh restart phoenix

# Port forward Phoenix service
./scripts/dev-helpers.sh port-forward phoenix-service 4000 80

# Show access information
./scripts/dev-helpers.sh access
```

## 🔄 Development Workflow

### **Quick Development Cycle**

```bash
# 1. Make code changes
# 2. Rebuild and update image
docker build -t phoenix-app:local .
./scripts/dev-helpers.sh update-image phoenix-app:local

# 3. Check logs
./scripts/dev-helpers.sh logs phoenix

# 4. Test your changes
curl http://localhost:30000/health
```

### Update Application
```bash
# Rebuild image
docker build -t phoenix-app:local .

# Load into cluster (minikube/kind)
minikube image load phoenix-app:local
# or
kind load docker-image phoenix-app:local

# Restart deployment
kubectl rollout restart deployment/phoenix-app -n phoenix-app-local
```

### Database Operations
```bash
# Run migrations
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "Phoenix.App.Release.migrate()"

# Seed database
kubectl exec -it deployment/phoenix-app -n phoenix-app-local -- /app/bin/phoenix_app eval "Phoenix.App.Release.seed()"

# Reset database
kubectl exec -it deployment/postgres -n phoenix-app-local -- psql -U postgres -c "DROP DATABASE IF EXISTS phoenix_dev; CREATE DATABASE phoenix_dev;"
```

### **Complete Cleanup**

```bash
# Use the automated cleanup script
./scripts/cleanup-local.sh

# Or force cleanup without confirmation
./scripts/cleanup-local.sh --force

# Or keep Docker images
./scripts/cleanup-local.sh --keep-images

# Manual cleanup
kubectl delete namespace phoenix-app-local

# Stop clusters
minikube stop          # For minikube
kind delete cluster    # For kind
```

## 📝 Notes

- This configuration is optimized for local development
- Security is relaxed (plain text secrets, no network policies)
- Resource limits are reduced for local machines
- Single replicas for all services
- No monitoring or advanced features

For production deployment, use the main Azure configuration in the parent directory.

## 🔗 Useful Commands

```bash
# Watch all pods
kubectl get pods -n phoenix-app-local -w

# Get all resources
kubectl get all -n phoenix-app-local

# Check resource usage
kubectl top pods -n phoenix-app-local
kubectl top nodes

# Describe ingress
kubectl describe ingress -n phoenix-app-local

# Check events
kubectl get events -n phoenix-app-local --sort-by='.lastTimestamp'

# View persistent volumes
kubectl get pv,pvc -n phoenix-app-local

# Check service endpoints
kubectl get endpoints -n phoenix-app-local
```

## 🆚 Local vs Production Comparison

| Aspect | Local Development | Production (Azure) |
|--------|------------------|-------------------|
| **Replicas** | 1 (single) | 3+ (high availability) |
| **Resources** | 200m CPU, 512Mi RAM | 500m CPU, 1Gi+ RAM |
| **Storage** | Local hostPath | Azure Premium SSD |
| **Secrets** | Plain text | Base64 encoded |
| **Environment** | `MIX_ENV=dev` | `MIX_ENV=prod` |
| **Logging** | Debug level | Info/warn level |
| **Security** | Relaxed | Network policies, RBAC |
| **Ingress** | NGINX/NodePort | Azure Application Gateway |
| **Monitoring** | Optional | Full observability stack |
| **Scaling** | Manual | Auto-scaling (HPA) |

## 📚 Additional Resources

- **Main Production Config**: `../` (Azure Kubernetes Service setup)
- **Phoenix Documentation**: https://hexdocs.pm/phoenix/
- **Docker Desktop Kubernetes**: https://docs.docker.com/desktop/kubernetes/
- **Minikube Documentation**: https://minikube.sigs.k8s.io/docs/
- **Kind Documentation**: https://kind.sigs.k8s.io/docs/
- **Kustomize Documentation**: https://kustomize.io/

## 🤝 Contributing to Local Development

When modifying the local development setup:

1. **Test all three Kubernetes options** (Docker Desktop, minikube, kind)
2. **Update documentation** for any new features or changes
3. **Keep resource requirements low** for developer laptops
4. **Maintain compatibility** with the production Azure setup
5. **Update helper scripts** for new functionality

## 💡 Tips & Best Practices

### **Performance Optimization**
- Use `imagePullPolicy: Never` for local images
- Set appropriate resource limits to avoid OOM kills
- Use persistent volumes for data that should survive restarts

### **Debugging**
- Use plain text secrets for easier debugging
- Enable debug logging in development
- Use port-forwarding for direct service access

### **Development Workflow**
- Use the helper scripts for common operations
- Keep Docker images small for faster builds
- Use health checks to ensure services are ready

### **Troubleshooting**
- Check pod logs first: `./scripts/dev-helpers.sh logs <component>`
- Verify service connectivity: `kubectl get endpoints -n phoenix-app-local`
- Use shell access for debugging: `./scripts/dev-helpers.sh shell <component>`
