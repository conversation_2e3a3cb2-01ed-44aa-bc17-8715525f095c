---
apiVersion: v1
kind: ConfigMap
metadata:
  name: phoenix-config
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: configmap
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Local development configuration for Phoenix application"
data:
  # Phoenix application configuration
  PHX_HOST: "localhost"
  PORT: "4000"
  MIX_ENV: "dev"
  
  # Database configuration (non-sensitive)
  DB_POOL_SIZE: "5"
  DB_TIMEOUT: "15000"
  DB_QUEUE_TARGET: "50"
  DB_QUEUE_INTERVAL: "1000"
  
  # Cache configuration
  CACHE_ADAPTER: "Nebulex.Adapters.Redis"
  CACHE_TTL: "3600"
  REDIS_HOST: "redis-service.phoenix-app-local.svc.cluster.local"
  REDIS_PORT: "6379"
  REDIS_DATABASE: "0"
  
  # Application settings
  LOG_LEVEL: "debug"
  ENABLE_TELEMETRY: "true"
  HEALTH_CHECK_PATH: "/health"
  READY_CHECK_PATH: "/ready"
  
  # Phoenix LiveView configuration
  LIVE_VIEW_SIGNING_SALT: "phoenix_live_view_dev"
  
  # Session configuration
  SESSION_TIMEOUT: "86400"
  
  # CORS configuration
  CORS_ORIGINS: "http://localhost:4000"
  
  # Rate limiting (disabled for local dev)
  RATE_LIMIT_ENABLED: "false"
  
  # Background job configuration
  SCHEDULER_ENABLED: "true"
  
  # Monitoring and observability
  PROMETHEUS_METRICS_ENABLED: "true"
  METRICS_PORT: "9090"
