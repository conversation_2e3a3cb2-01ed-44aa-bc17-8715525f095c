---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Redis service for local development"
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
