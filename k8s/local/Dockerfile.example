# Example Dockerfile for Phoenix Elixir Application
# Place this file in your Phoenix project root directory

# Multi-stage build for optimized production image
FROM elixir:1.15-alpine AS build

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    npm \
    git \
    python3 \
    curl

# Prepare build directory
WORKDIR /app

# Install hex and rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Set build environment
ENV MIX_ENV=prod

# Install mix dependencies
COPY mix.exs mix.lock ./
COPY config config
RUN mix do deps.get, deps.compile

# Build assets (if you have assets)
COPY assets/package.json assets/package-lock.json ./assets/
RUN npm --prefix ./assets ci --progress=false --no-audit --loglevel=error

COPY priv priv
COPY assets assets
RUN npm run --prefix ./assets deploy
RUN mix phx.digest

# Compile and build release
COPY lib lib
# Copy any additional directories your app needs
# COPY rel rel

RUN mix do compile, release

# Prepare release image
FROM alpine:3.18 AS app

# Install runtime dependencies
RUN apk add --no-cache \
    openssl \
    ncurses-libs \
    curl \
    bash

# Create app user
RUN addgroup -g 1000 -S phoenix && \
    adduser -u 1000 -S phoenix -G phoenix

# Create app directory
WORKDIR /app

# Change ownership
RUN chown phoenix:phoenix /app

# Switch to non-root user
USER phoenix:phoenix

# Copy release from build stage
COPY --from=build --chown=phoenix:phoenix /app/_build/prod/rel/phoenix_app ./

# Set environment
ENV HOME=/app
ENV MIX_ENV=prod
ENV PORT=4000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:4000/health || exit 1

# Expose port
EXPOSE 4000

# Start the application
CMD ["bin/phoenix_app", "start"]

# Alternative commands for different environments:
# Development: CMD ["bin/phoenix_app", "start"]
# Production:  CMD ["bin/phoenix_app", "start"]
# Console:     CMD ["bin/phoenix_app", "remote"]
