#!/bin/bash

# Phoenix Local Development Setup Script
# This script sets up the complete local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="phoenix-app-local"
PHOENIX_IMAGE="phoenix-app:local"
CONTEXT=""

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Kubernetes cluster is running
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Kubernetes cluster is not accessible. Please start your local cluster."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

detect_k8s_environment() {
    log_info "Detecting Kubernetes environment..."
    
    # Check for Docker Desktop
    if kubectl config current-context | grep -q "docker-desktop"; then
        CONTEXT="docker-desktop"
        log_info "Detected: Docker Desktop Kubernetes"
    # Check for minikube
    elif kubectl config current-context | grep -q "minikube"; then
        CONTEXT="minikube"
        log_info "Detected: Minikube"
    # Check for kind
    elif kubectl config current-context | grep -q "kind"; then
        CONTEXT="kind"
        log_info "Detected: kind (Kubernetes in Docker)"
    else
        CONTEXT="unknown"
        log_warning "Unknown Kubernetes context. Proceeding with generic setup."
    fi
}

build_phoenix_image() {
    log_info "Building Phoenix application image..."
    
    # Check if Dockerfile exists
    if [ ! -f "../../Dockerfile" ]; then
        log_warning "Dockerfile not found in project root. Please create one first."
        log_info "Example Dockerfile has been provided in the documentation."
        return 1
    fi
    
    # Build the image
    docker build -t $PHOENIX_IMAGE ../../
    
    # Load image into cluster based on context
    case $CONTEXT in
        "minikube")
            log_info "Loading image into minikube..."
            minikube image load $PHOENIX_IMAGE
            ;;
        "kind")
            log_info "Loading image into kind cluster..."
            kind load docker-image $PHOENIX_IMAGE
            ;;
        "docker-desktop")
            log_info "Image available in Docker Desktop"
            ;;
        *)
            log_warning "Unknown context. Image may not be available in cluster."
            ;;
    esac
    
    log_success "Phoenix image built and loaded"
}

deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Create namespace
    kubectl apply -f ../namespace.yaml
    
    # Apply storage configuration
    kubectl apply -f ../local-storage.yaml
    
    # Apply configuration
    kubectl apply -f ../phoenix-configmap.yaml
    kubectl apply -f ../phoenix-secret.yaml
    
    log_success "Infrastructure deployed"
}

deploy_database() {
    log_info "Deploying PostgreSQL database..."
    
    kubectl apply -f ../postgres-deployment.yaml
    kubectl apply -f ../postgres-service.yaml
    
    log_info "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n $NAMESPACE --timeout=300s
    
    log_success "PostgreSQL deployed and ready"
}

deploy_cache() {
    log_info "Deploying Redis cache..."
    
    kubectl apply -f ../redis-deployment.yaml
    kubectl apply -f ../redis-service.yaml
    
    log_info "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n $NAMESPACE --timeout=300s
    
    log_success "Redis deployed and ready"
}

deploy_application() {
    log_info "Deploying Phoenix application..."
    
    # Update image in deployment file
    sed -i.bak "s|your-phoenix-image:latest|$PHOENIX_IMAGE|g" ../phoenix-deployment.yaml
    
    kubectl apply -f ../phoenix-deployment.yaml
    kubectl apply -f ../phoenix-service.yaml
    
    log_info "Waiting for Phoenix application to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=phoenix-app -n $NAMESPACE --timeout=300s
    
    # Restore original deployment file
    mv ../phoenix-deployment.yaml.bak ../phoenix-deployment.yaml
    
    log_success "Phoenix application deployed and ready"
}

deploy_dev_tools() {
    log_info "Deploying development tools..."
    
    kubectl apply -f ../dev-tools.yaml
    
    log_info "Waiting for development tools to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=pgadmin -n $NAMESPACE --timeout=300s || true
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis-commander -n $NAMESPACE --timeout=300s || true
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=mailhog -n $NAMESPACE --timeout=300s || true
    
    log_success "Development tools deployed"
}

deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    kubectl apply -f ../monitoring-local.yaml
    
    log_info "Waiting for monitoring components to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus -n $NAMESPACE --timeout=300s || true
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n $NAMESPACE --timeout=300s || true
    
    log_success "Monitoring stack deployed"
}

setup_ingress() {
    log_info "Setting up ingress..."
    
    case $CONTEXT in
        "minikube")
            log_info "Enabling ingress addon for minikube..."
            minikube addons enable ingress
            kubectl apply -f ../local-ingress.yaml
            ;;
        "kind"|"docker-desktop")
            log_info "Applying ingress configuration..."
            kubectl apply -f ../local-ingress.yaml
            ;;
        *)
            log_warning "Skipping ingress setup for unknown context"
            ;;
    esac
}

show_access_info() {
    log_success "Deployment completed successfully!"
    echo
    log_info "Access Information:"
    echo "===================="
    
    case $CONTEXT in
        "minikube")
            MINIKUBE_IP=$(minikube ip)
            echo "Phoenix App:      http://$MINIKUBE_IP:30000"
            echo "pgAdmin:          http://$MINIKUBE_IP:30001 (<EMAIL> / admin)"
            echo "Redis Commander:  http://$MINIKUBE_IP:30002 (admin / admin)"
            echo "Mailhog:          http://$MINIKUBE_IP:30003"
            echo "Prometheus:       http://$MINIKUBE_IP:30004"
            echo "Grafana:          http://$MINIKUBE_IP:30005 (admin / admin)"
            ;;
        *)
            echo "Phoenix App:      http://localhost:30000"
            echo "pgAdmin:          http://localhost:30001 (<EMAIL> / admin)"
            echo "Redis Commander:  http://localhost:30002 (admin / admin)"
            echo "Mailhog:          http://localhost:30003"
            echo "Prometheus:       http://localhost:30004"
            echo "Grafana:          http://localhost:30005 (admin / admin)"
            ;;
    esac
    
    echo
    log_info "Alternative access methods:"
    echo "Port forward: kubectl port-forward service/phoenix-service 4000:80 -n $NAMESPACE"
    echo "Ingress:      http://phoenix.local (add to /etc/hosts)"
    echo
    log_info "Useful commands:"
    echo "View pods:    kubectl get pods -n $NAMESPACE"
    echo "View logs:    kubectl logs -f deployment/phoenix-app -n $NAMESPACE"
    echo "Shell access: kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /bin/sh"
}

cleanup() {
    log_info "Cleaning up on exit..."
    # Restore deployment file if backup exists
    if [ -f "../phoenix-deployment.yaml.bak" ]; then
        mv ../phoenix-deployment.yaml.bak ../phoenix-deployment.yaml
    fi
}

# Main execution
main() {
    log_info "Starting Phoenix Local Development Environment Setup"
    echo "=================================================="
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Check if we should build the image
    BUILD_IMAGE=true
    if [ "$1" = "--skip-build" ]; then
        BUILD_IMAGE=false
        log_info "Skipping image build as requested"
    fi
    
    # Execute setup steps
    check_prerequisites
    detect_k8s_environment
    
    if [ "$BUILD_IMAGE" = true ]; then
        build_phoenix_image
    fi
    
    deploy_infrastructure
    deploy_database
    deploy_cache
    deploy_application
    
    # Optional components
    if [ "$1" != "--minimal" ]; then
        deploy_dev_tools
        deploy_monitoring
        setup_ingress
    fi
    
    show_access_info
}

# Help function
show_help() {
    echo "Phoenix Local Development Environment Setup"
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --skip-build    Skip building the Phoenix Docker image"
    echo "  --minimal       Deploy only core components (no dev tools or monitoring)"
    echo "  --help          Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Full setup with image build"
    echo "  $0 --skip-build       # Setup without building image"
    echo "  $0 --minimal          # Minimal setup (app, db, cache only)"
}

# Parse arguments
case "$1" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
