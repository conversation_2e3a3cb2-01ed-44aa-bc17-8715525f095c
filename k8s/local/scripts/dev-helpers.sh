#!/bin/bash

# Phoenix Local Development Helper Scripts
# Collection of useful commands for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="phoenix-app-local"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Status functions
show_status() {
    log_info "Phoenix Local Development Environment Status"
    echo "=============================================="
    
    echo
    log_info "Namespace:"
    kubectl get namespace $NAMESPACE 2>/dev/null || echo "Namespace not found"
    
    echo
    log_info "Pods:"
    kubectl get pods -n $NAMESPACE -o wide 2>/dev/null || echo "No pods found"
    
    echo
    log_info "Services:"
    kubectl get services -n $NAMESPACE 2>/dev/null || echo "No services found"
    
    echo
    log_info "Persistent Volume Claims:"
    kubectl get pvc -n $NAMESPACE 2>/dev/null || echo "No PVCs found"
    
    echo
    log_info "Ingress:"
    kubectl get ingress -n $NAMESPACE 2>/dev/null || echo "No ingress found"
}

show_logs() {
    local component=${1:-phoenix-app}
    
    log_info "Showing logs for: $component"
    
    case $component in
        "phoenix"|"app")
            kubectl logs -f deployment/phoenix-app -n $NAMESPACE
            ;;
        "postgres"|"db")
            kubectl logs -f deployment/postgres -n $NAMESPACE
            ;;
        "redis"|"cache")
            kubectl logs -f deployment/redis -n $NAMESPACE
            ;;
        "pgadmin")
            kubectl logs -f deployment/pgadmin -n $NAMESPACE
            ;;
        "redis-commander")
            kubectl logs -f deployment/redis-commander -n $NAMESPACE
            ;;
        "mailhog")
            kubectl logs -f deployment/mailhog -n $NAMESPACE
            ;;
        "prometheus")
            kubectl logs -f deployment/prometheus -n $NAMESPACE
            ;;
        "grafana")
            kubectl logs -f deployment/grafana -n $NAMESPACE
            ;;
        *)
            log_error "Unknown component: $component"
            echo "Available components: phoenix, postgres, redis, pgadmin, redis-commander, mailhog, prometheus, grafana"
            exit 1
            ;;
    esac
}

shell_access() {
    local component=${1:-phoenix-app}
    
    log_info "Opening shell for: $component"
    
    case $component in
        "phoenix"|"app")
            kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /bin/sh
            ;;
        "postgres"|"db")
            kubectl exec -it deployment/postgres -n $NAMESPACE -- /bin/sh
            ;;
        "redis"|"cache")
            kubectl exec -it deployment/redis -n $NAMESPACE -- /bin/sh
            ;;
        *)
            log_error "Shell access not available for: $component"
            echo "Available components: phoenix, postgres, redis"
            exit 1
            ;;
    esac
}

port_forward() {
    local service=${1:-phoenix-service}
    local local_port=${2:-4000}
    local remote_port=${3:-80}
    
    log_info "Port forwarding $service:$remote_port to localhost:$local_port"
    log_info "Press Ctrl+C to stop"
    
    kubectl port-forward service/$service $local_port:$remote_port -n $NAMESPACE
}

database_operations() {
    local operation=$1
    
    case $operation in
        "connect"|"psql")
            log_info "Connecting to PostgreSQL database..."
            kubectl exec -it deployment/postgres -n $NAMESPACE -- psql -U postgres -d phoenix_dev
            ;;
        "migrate")
            log_info "Running database migrations..."
            kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /app/bin/phoenix_app eval "Phoenix.App.Release.migrate()"
            ;;
        "seed")
            log_info "Seeding database..."
            kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /app/bin/phoenix_app eval "Phoenix.App.Release.seed()"
            ;;
        "reset")
            log_warning "This will drop and recreate the database!"
            read -p "Are you sure? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                kubectl exec -it deployment/postgres -n $NAMESPACE -- psql -U postgres -c "DROP DATABASE IF EXISTS phoenix_dev; CREATE DATABASE phoenix_dev;"
                log_success "Database reset completed"
            fi
            ;;
        "backup")
            local backup_file="phoenix_dev_backup_$(date +%Y%m%d_%H%M%S).sql"
            log_info "Creating database backup: $backup_file"
            kubectl exec deployment/postgres -n $NAMESPACE -- pg_dump -U postgres phoenix_dev > $backup_file
            log_success "Backup created: $backup_file"
            ;;
        *)
            log_error "Unknown database operation: $operation"
            echo "Available operations: connect, migrate, seed, reset, backup"
            exit 1
            ;;
    esac
}

restart_component() {
    local component=${1:-phoenix-app}
    
    log_info "Restarting: $component"
    
    case $component in
        "phoenix"|"app")
            kubectl rollout restart deployment/phoenix-app -n $NAMESPACE
            kubectl rollout status deployment/phoenix-app -n $NAMESPACE
            ;;
        "postgres"|"db")
            kubectl rollout restart deployment/postgres -n $NAMESPACE
            kubectl rollout status deployment/postgres -n $NAMESPACE
            ;;
        "redis"|"cache")
            kubectl rollout restart deployment/redis -n $NAMESPACE
            kubectl rollout status deployment/redis -n $NAMESPACE
            ;;
        "all")
            kubectl rollout restart deployment/phoenix-app -n $NAMESPACE
            kubectl rollout restart deployment/postgres -n $NAMESPACE
            kubectl rollout restart deployment/redis -n $NAMESPACE
            log_info "Waiting for all deployments to be ready..."
            kubectl rollout status deployment/phoenix-app -n $NAMESPACE
            kubectl rollout status deployment/postgres -n $NAMESPACE
            kubectl rollout status deployment/redis -n $NAMESPACE
            ;;
        *)
            log_error "Unknown component: $component"
            echo "Available components: phoenix, postgres, redis, all"
            exit 1
            ;;
    esac
    
    log_success "Restart completed for: $component"
}

update_image() {
    local new_image=${1:-phoenix-app:local}
    
    log_info "Updating Phoenix application image to: $new_image"
    
    # Update the deployment
    kubectl set image deployment/phoenix-app phoenix-app=$new_image -n $NAMESPACE
    
    # Wait for rollout to complete
    kubectl rollout status deployment/phoenix-app -n $NAMESPACE
    
    log_success "Image updated successfully"
}

show_access_info() {
    log_info "Access Information"
    echo "=================="
    
    # Detect environment
    if kubectl config current-context | grep -q "minikube"; then
        MINIKUBE_IP=$(minikube ip)
        echo "Phoenix App:      http://$MINIKUBE_IP:30000"
        echo "pgAdmin:          http://$MINIKUBE_IP:30001"
        echo "Redis Commander:  http://$MINIKUBE_IP:30002"
        echo "Mailhog:          http://$MINIKUBE_IP:30003"
        echo "Prometheus:       http://$MINIKUBE_IP:30004"
        echo "Grafana:          http://$MINIKUBE_IP:30005"
    else
        echo "Phoenix App:      http://localhost:30000"
        echo "pgAdmin:          http://localhost:30001"
        echo "Redis Commander:  http://localhost:30002"
        echo "Mailhog:          http://localhost:30003"
        echo "Prometheus:       http://localhost:30004"
        echo "Grafana:          http://localhost:30005"
    fi
    
    echo
    echo "Credentials:"
    echo "  pgAdmin:          <EMAIL> / admin"
    echo "  Redis Commander:  admin / admin"
    echo "  Grafana:          admin / admin"
}

run_tests() {
    log_info "Running Phoenix tests..."
    kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /app/bin/phoenix_app eval "Mix.Task.run(\"test\")"
}

iex_console() {
    log_info "Opening IEx console..."
    kubectl exec -it deployment/phoenix-app -n $NAMESPACE -- /app/bin/phoenix_app remote
}

# Help function
show_help() {
    echo "Phoenix Local Development Helper Scripts"
    echo "Usage: $0 COMMAND [OPTIONS]"
    echo
    echo "Commands:"
    echo "  status                    Show environment status"
    echo "  logs [component]          Show logs (phoenix, postgres, redis, etc.)"
    echo "  shell [component]         Open shell (phoenix, postgres, redis)"
    echo "  port-forward [service] [local_port] [remote_port]"
    echo "  db [operation]            Database operations (connect, migrate, seed, reset, backup)"
    echo "  restart [component]       Restart component (phoenix, postgres, redis, all)"
    echo "  update-image [image]      Update Phoenix application image"
    echo "  access                    Show access information"
    echo "  test                      Run Phoenix tests"
    echo "  console                   Open IEx console"
    echo "  help                      Show this help message"
    echo
    echo "Examples:"
    echo "  $0 status                 # Show environment status"
    echo "  $0 logs phoenix           # Show Phoenix application logs"
    echo "  $0 shell postgres         # Open PostgreSQL shell"
    echo "  $0 port-forward phoenix-service 4000 80"
    echo "  $0 db migrate             # Run database migrations"
    echo "  $0 restart phoenix        # Restart Phoenix application"
    echo "  $0 update-image phoenix-app:v2"
}

# Main execution
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    case "$1" in
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            shell_access "$2"
            ;;
        "port-forward")
            port_forward "$2" "$3" "$4"
            ;;
        "db")
            database_operations "$2"
            ;;
        "restart")
            restart_component "$2"
            ;;
        "update-image")
            update_image "$2"
            ;;
        "access")
            show_access_info
            ;;
        "test")
            run_tests
            ;;
        "console")
            iex_console
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
