#!/bin/bash

# Phoenix Local Development Cleanup Script
# This script cleans up the local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="phoenix-app-local"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

confirm_cleanup() {
    echo -e "${YELLOW}WARNING: This will delete all resources in the '$NAMESPACE' namespace.${NC}"
    echo "This includes:"
    echo "  - Phoenix application and data"
    echo "  - PostgreSQL database and data"
    echo "  - Redis cache and data"
    echo "  - All development tools"
    echo "  - All monitoring components"
    echo
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cleanup cancelled"
        exit 0
    fi
}

cleanup_namespace() {
    log_info "Cleaning up namespace: $NAMESPACE"
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        # Delete the entire namespace (this removes all resources)
        kubectl delete namespace $NAMESPACE
        
        # Wait for namespace to be fully deleted
        log_info "Waiting for namespace to be fully deleted..."
        while kubectl get namespace $NAMESPACE &> /dev/null; do
            sleep 2
        done
        
        log_success "Namespace '$NAMESPACE' deleted successfully"
    else
        log_warning "Namespace '$NAMESPACE' does not exist"
    fi
}

cleanup_persistent_volumes() {
    log_info "Cleaning up persistent volumes..."
    
    # Delete local persistent volumes
    kubectl delete pv postgres-pv-local --ignore-not-found=true
    kubectl delete pv redis-pv-local --ignore-not-found=true
    
    log_success "Persistent volumes cleaned up"
}

cleanup_local_data() {
    log_info "Cleaning up local data directories..."
    
    # Remove local data directories (if they exist)
    if [ -d "/tmp/phoenix-postgres-data" ]; then
        sudo rm -rf /tmp/phoenix-postgres-data
        log_info "Removed PostgreSQL data directory"
    fi
    
    if [ -d "/tmp/phoenix-redis-data" ]; then
        sudo rm -rf /tmp/phoenix-redis-data
        log_info "Removed Redis data directory"
    fi
    
    log_success "Local data directories cleaned up"
}

cleanup_docker_images() {
    log_info "Cleaning up Docker images..."
    
    # Remove Phoenix application image
    if docker images | grep -q "phoenix-app.*local"; then
        docker rmi phoenix-app:local || log_warning "Could not remove phoenix-app:local image"
        log_info "Removed Phoenix application image"
    fi
    
    # Optionally clean up unused images
    read -p "Do you want to clean up unused Docker images? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker image prune -f
        log_info "Cleaned up unused Docker images"
    fi
    
    log_success "Docker cleanup completed"
}

reset_ingress() {
    log_info "Resetting ingress configuration..."
    
    # Check if we're using minikube
    if kubectl config current-context | grep -q "minikube"; then
        log_info "Disabling minikube ingress addon..."
        minikube addons disable ingress || log_warning "Could not disable ingress addon"
    fi
    
    log_success "Ingress reset completed"
}

show_cleanup_summary() {
    log_success "Cleanup completed successfully!"
    echo
    log_info "What was cleaned up:"
    echo "===================="
    echo "✓ Kubernetes namespace and all resources"
    echo "✓ Persistent volumes"
    echo "✓ Local data directories"
    echo "✓ Docker images (if selected)"
    echo "✓ Ingress configuration"
    echo
    log_info "Your local Kubernetes cluster is still running."
    log_info "To stop it:"
    echo "  Docker Desktop: Disable Kubernetes in settings"
    echo "  Minikube:       minikube stop"
    echo "  Kind:           kind delete cluster"
}

# Help function
show_help() {
    echo "Phoenix Local Development Environment Cleanup"
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --force         Skip confirmation prompt"
    echo "  --keep-images   Don't remove Docker images"
    echo "  --help          Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Interactive cleanup"
    echo "  $0 --force            # Force cleanup without confirmation"
    echo "  $0 --keep-images      # Cleanup but keep Docker images"
}

# Main execution
main() {
    log_info "Phoenix Local Development Environment Cleanup"
    echo "=============================================="
    
    # Parse arguments
    FORCE=false
    KEEP_IMAGES=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE=true
                shift
                ;;
            --keep-images)
                KEEP_IMAGES=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Confirm cleanup unless forced
    if [ "$FORCE" = false ]; then
        confirm_cleanup
    fi
    
    # Execute cleanup steps
    cleanup_namespace
    cleanup_persistent_volumes
    cleanup_local_data
    
    if [ "$KEEP_IMAGES" = false ]; then
        cleanup_docker_images
    fi
    
    reset_ingress
    show_cleanup_summary
}

# Execute main function
main "$@"
