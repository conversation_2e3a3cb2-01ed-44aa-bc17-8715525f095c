---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: phoenix-app
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-server
    app.kubernetes.io/part-of: phoenix-application
    app.kubernetes.io/version: "latest"
  annotations:
    description: "Phoenix Elixir web application for local development"
spec:
  replicas: 1  # Single replica for local development
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: phoenix-app
      app.kubernetes.io/component: web-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: phoenix-app
        app.kubernetes.io/component: web-server
        app.kubernetes.io/part-of: phoenix-application
        app.kubernetes.io/version: "latest"
    spec:
      # Init container for database migrations
      initContainers:
      - name: migrate
        image: "your-phoenix-image:latest"  # Replace with your image
        imagePullPolicy: IfNotPresent
        
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for database to be ready..."
          until pg_isready -h postgres-service.phoenix-app-local.svc.cluster.local -p 5432 -U postgres; do
            echo "Waiting for database..."
            sleep 2
          done
          echo "Running database migrations..."
          /app/bin/phoenix_app eval "Phoenix.App.Release.migrate()"
        
        env:
        - name: MIX_ENV
          valueFrom:
            configMapKeyRef:
              name: phoenix-config
              key: MIX_ENV
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DATABASE_URL
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: SECRET_KEY_BASE
        
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      
      containers:
      - name: phoenix-app
        image: "your-phoenix-image:latest"  # Replace with your image
        imagePullPolicy: IfNotPresent
        
        # Resource management (reduced for local)
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        
        # Environment variables from ConfigMap
        envFrom:
        - configMapRef:
            name: phoenix-config
        
        # Environment variables from Secrets
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: DATABASE_URL
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: SECRET_KEY_BASE
        - name: LIVE_VIEW_SIGNING_SALT_SECRET
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: LIVE_VIEW_SIGNING_SALT_SECRET
        - name: GUARDIAN_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: GUARDIAN_SECRET_KEY
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: JWT_SECRET
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: phoenix-secrets
              key: ENCRYPTION_KEY
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        # Startup probe for slow-starting applications
        startupProbe:
          httpGet:
            path: /health
            port: 4000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        
        # Ports
        ports:
        - name: http
          containerPort: 4000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        
        # Volume mounts for temporary files
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      
      # Volumes
      volumes:
      - name: tmp
        emptyDir: {}
      
      # Restart policy
      restartPolicy: Always
      
      # Termination grace period
      terminationGracePeriodSeconds: 30
