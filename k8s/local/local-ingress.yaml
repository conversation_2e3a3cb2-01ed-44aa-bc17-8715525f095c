---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: phoenix-ingress
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Local ingress for Phoenix application"
    # Use nginx ingress controller for local development
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: phoenix.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
      
      # Health check endpoint
      - path: /health
        pathType: Exact
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
      
      # Metrics endpoint
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: phoenix-service
            port:
              number: 9090
  
  # Alternative: localhost access
  - host: localhost
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: phoenix-service
            port:
              number: 80
