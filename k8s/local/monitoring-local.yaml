---
# Simple Prometheus for local development
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    scrape_configs:
    - job_name: 'phoenix-app'
      static_configs:
      - targets: ['phoenix-service.phoenix-app-local.svc.cluster.local:9090']
      metrics_path: /metrics
      scrape_interval: 30s

    - job_name: 'postgres'
      static_configs:
      - targets: ['postgres-service.phoenix-app-local.svc.cluster.local:9187']
      metrics_path: /metrics
      scrape_interval: 30s

    - job_name: 'redis'
      static_configs:
      - targets: ['redis-service.phoenix-app-local.svc.cluster.local:9121']
      metrics_path: /metrics
      scrape_interval: 30s

    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - phoenix-app-local
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus
  template:
    metadata:
      labels:
        app.kubernetes.io/name: prometheus
        app.kubernetes.io/component: monitoring
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        
        args:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus/'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--storage.tsdb.retention.time=7d'
        - '--web.enable-lifecycle'
        
        ports:
        - name: web
          containerPort: 9090
          protocol: TCP
        
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/prometheus.yml
          subPath: prometheus.yml
          readOnly: true
        - name: prometheus-storage
          mountPath: /prometheus
      
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/component: monitoring-service
spec:
  type: NodePort
  ports:
  - name: web
    port: 9090
    targetPort: 9090
    nodePort: 30004
    protocol: TCP
  selector:
    app.kubernetes.io/name: prometheus
---
# Simple Grafana for visualization
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: datasources
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus-service.phoenix-app-local.svc.cluster.local:9090
      isDefault: true
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: visualization
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
  template:
    metadata:
      labels:
        app.kubernetes.io/name: grafana
        app.kubernetes.io/component: visualization
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        imagePullPolicy: IfNotPresent
        
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin"
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
        
        ports:
        - name: web
          containerPort: 3000
          protocol: TCP
        
        volumeMounts:
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
          readOnly: true
        - name: grafana-storage
          mountPath: /var/lib/grafana
      
      volumes:
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/component: visualization-service
spec:
  type: NodePort
  ports:
  - name: web
    port: 3000
    targetPort: 3000
    nodePort: 30005
    protocol: TCP
  selector:
    app.kubernetes.io/name: grafana
