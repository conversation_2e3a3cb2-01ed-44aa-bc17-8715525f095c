# kind cluster configuration for local Phoenix development
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: phoenix-local

# Cluster-wide configuration
networking:
  # Disable default CNI to use Calico (optional)
  disableDefaultCNI: false
  # Set pod and service subnets
  podSubnet: "**********/16"
  serviceSubnet: "*********/16"

# Node configuration
nodes:
# Control plane node
- role: control-plane
  image: kindest/node:v1.28.0
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  # Phoenix application
  - containerPort: 30000
    hostPort: 30000
    protocol: TCP
  # Metrics
  - containerPort: 30090
    hostPort: 30090
    protocol: TCP
  # Ingress controller
  - containerPort: 80
    hostPort: 80
    protocol: TCP
  - containerPort: 443
    hostPort: 443
    protocol: TCP

# Worker nodes (optional, for testing multi-node scenarios)
- role: worker
  image: kindest/node:v1.28.0
  
# Additional worker node (optional)
# - role: worker
#   image: kindest/node:v1.28.0

# Feature gates (optional)
featureGates:
  # Enable feature gates if needed
  # "SomeFeature": true

# Runtime configuration
runtimeConfig:
  # Enable APIs if needed
  # "api/all": "true"
