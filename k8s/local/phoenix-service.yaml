---
apiVersion: v1
kind: Service
metadata:
  name: phoenix-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Phoenix service for local development"
spec:
  type: NodePort  # NodePort for local access
  ports:
  - name: http
    port: 80
    targetPort: 4000
    nodePort: 30000  # Access via localhost:30000
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    nodePort: 30090  # Metrics via localhost:30090
    protocol: TCP
  selector:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: web-server
