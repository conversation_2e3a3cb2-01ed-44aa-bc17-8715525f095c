# Alternative: Docker Compose for even simpler local development
# Use this if you prefer Docker Compose over Kubernetes for local dev

version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: phoenix-postgres-local
    environment:
      POSTGRES_DB: phoenix_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d phoenix_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - phoenix-network

  redis:
    image: redis:7-alpine
    container_name: phoenix-redis-local
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - phoenix-network

  phoenix:
    # Build from your Phoenix application
    build:
      context: ../..  # Adjust path to your Phoenix app root
      dockerfile: Dockerfile
    # Alternative: use pre-built image
    # image: phoenix-app:local
    container_name: phoenix-app-local
    environment:
      # Database configuration
      DATABASE_URL: "ecto://postgres:postgres@postgres:5432/phoenix_dev"
      
      # Phoenix configuration
      PHX_HOST: "localhost"
      PORT: "4000"
      MIX_ENV: "dev"
      SECRET_KEY_BASE: "your-secret-key-base-for-local-development-change-this-in-production"
      
      # Cache configuration
      REDIS_HOST: "redis"
      REDIS_PORT: "6379"
      REDIS_DATABASE: "0"
      
      # Application settings
      LOG_LEVEL: "debug"
      ENABLE_TELEMETRY: "true"
      
      # LiveView configuration
      LIVE_VIEW_SIGNING_SALT: "phoenix_live_view_dev"
      
      # Other secrets (for development)
      GUARDIAN_SECRET_KEY: "your-guardian-secret-key-for-local-development"
      JWT_SECRET: "your-jwt-secret-for-local-development"
      ENCRYPTION_KEY: "your-encryption-key-for-local-development-32-chars"
    
    ports:
      - "4000:4000"  # Phoenix application
      - "9090:9090"  # Metrics (if enabled)
    
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
    
    networks:
      - phoenix-network

  # Optional: Database administration tool
  adminer:
    image: adminer:4.8.1
    container_name: phoenix-adminer-local
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - phoenix-network

  # Optional: Redis administration tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: phoenix-redis-commander-local
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: "local:redis:6379"
      HTTP_USER: "admin"
      HTTP_PASSWORD: "admin"
    depends_on:
      - redis
    networks:
      - phoenix-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  phoenix-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
