---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-service
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "PostgreSQL service for local development"
spec:
  type: ClusterIP
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
