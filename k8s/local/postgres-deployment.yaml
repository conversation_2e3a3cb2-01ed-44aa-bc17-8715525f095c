---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: phoenix-application
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: phoenix-application
    app.kubernetes.io/version: "14"
  annotations:
    description: "PostgreSQL database for local Phoenix development"
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres
      app.kubernetes.io/component: database
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres
        app.kubernetes.io/component: database
        app.kubernetes.io/part-of: phoenix-application
        app.kubernetes.io/version: "14"
    spec:
      containers:
      - name: postgres
        image: postgres:14-alpine
        imagePullPolicy: IfNotPresent
        
        # Resource management (reduced for local)
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        
        # Environment variables
        env:
        - name: POSTGRES_DB
          value: "phoenix_dev"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "postgres"
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        
        # Health checks
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U postgres -d phoenix_dev -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U postgres -d phoenix_dev -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Volume mounts
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        
        # Ports
        ports:
        - name: postgres
          containerPort: 5432
          protocol: TCP
      
      # Volumes
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
