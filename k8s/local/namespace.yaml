---
apiVersion: v1
kind: Namespace
metadata:
  name: phoenix-app-local
  labels:
    name: phoenix-app-local
    environment: development
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: namespace
  annotations:
    description: "Local development namespace for Phoenix application"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: phoenix-app-local-quota
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: resource-quota
spec:
  hard:
    requests.cpu: "2"
    requests.memory: 4Gi
    limits.cpu: "4"
    limits.memory: 8Gi
    persistentvolumeclaims: "5"
    pods: "10"
    services: "5"
    secrets: "5"
    configmaps: "5"
