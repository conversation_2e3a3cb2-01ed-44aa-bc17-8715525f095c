---
apiVersion: v1
kind: Secret
metadata:
  name: phoenix-secrets
  namespace: phoenix-app-local
  labels:
    app.kubernetes.io/name: phoenix-app
    app.kubernetes.io/component: secret
    app.kubernetes.io/part-of: phoenix-application
  annotations:
    description: "Local development secrets for Phoenix application"
type: Opaque
stringData:
  # Database credentials (plain text for local dev)
  DATABASE_URL: "ecto://postgres:<EMAIL>:5432/phoenix_dev"
  DB_USERNAME: "postgres"
  DB_PASSWORD: "postgres"
  
  # Phoenix secret key base (development key)
  SECRET_KEY_BASE: "your-secret-key-base-for-local-development-change-this-in-production"
  
  # LiveView signing salt
  LIVE_VIEW_SIGNING_SALT_SECRET: "your-live-view-signing-salt-for-local-dev"
  
  # Guardian secret key
  GUARDIAN_SECRET_KEY: "your-guardian-secret-key-for-local-development"
  
  # Redis password (empty for local dev)
  REDIS_PASSWORD: ""
  
  # JWT signing key
  JWT_SECRET: "your-jwt-secret-for-local-development"
  
  # Encryption key for sensitive data
  ENCRYPTION_KEY: "your-encryption-key-for-local-development-32-chars"
