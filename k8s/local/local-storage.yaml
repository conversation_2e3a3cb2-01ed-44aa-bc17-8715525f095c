---
# Local storage class for development
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  labels:
    app.kubernetes.io/name: local-storage
    app.kubernetes.io/component: storage-class
  annotations:
    description: "Local storage class for development"
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
allowVolumeExpansion: false
---
# Persistent volumes for local development
apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgres-pv-local
  labels:
    app.kubernetes.io/name: postgres
    app.kubernetes.io/component: database-storage
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: local-storage
  hostPath:
    path: /tmp/phoenix-postgres-data
    type: DirectoryOrCreate
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv-local
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache-storage
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: local-storage
  hostPath:
    path: /tmp/phoenix-redis-data
    type: DirectoryOrCreate
