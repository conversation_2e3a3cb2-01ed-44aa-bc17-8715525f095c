{"version": "2.0.0", "tasks": [{"label": "mix: phx.server", "type": "shell", "command": "mix", "args": ["phx.server"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}, "dependsOn": ["mix: deps.get"]}, {"label": "mix: test", "type": "shell", "command": "mix", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [{"owner": "elixir", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^\\s*\\d+\\)\\s+(.*)$", "file": 1}}]}, {"label": "mix: test.watch", "type": "shell", "command": "mix", "args": ["test.watch"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated"}, "isBackground": true, "problemMatcher": []}, {"label": "mix: test --cover", "type": "shell", "command": "mix", "args": ["test", "--cover"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: deps.get", "type": "shell", "command": "mix", "args": ["deps.get"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: deps.compile", "type": "shell", "command": "mix", "args": ["deps.compile"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": [], "dependsOn": ["mix: deps.get"]}, {"label": "mix: compile", "type": "shell", "command": "mix", "args": ["compile"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": [{"owner": "elixir", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^\\*\\* \\((\\w+)\\) (.+):(\\d+): (.+)$", "file": 2, "line": 3, "message": 4, "severity": 1}}], "dependsOn": ["mix: deps.compile"]}, {"label": "mix: format", "type": "shell", "command": "mix", "args": ["format"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: credo", "type": "shell", "command": "mix", "args": ["credo", "--strict"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [{"owner": "credo", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.+):(\\d+):(\\d+): ([A-Z]): (.+)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}]}, {"label": "mix: dialyzer", "type": "shell", "command": "mix", "args": ["dialyzer"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: ecto.create", "type": "shell", "command": "mix", "args": ["ecto.create"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: ecto.migrate", "type": "shell", "command": "mix", "args": ["ecto.migrate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: ecto.rollback", "type": "shell", "command": "mix", "args": ["ecto.rollback"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: ecto.reset", "type": "shell", "command": "mix", "args": ["ecto.reset"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "mix: test.setup", "type": "shell", "command": "mix", "args": ["do", "ecto.create,", "ecto.migrate"], "options": {"env": {"MIX_ENV": "test"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "npm: install", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/assets"}, "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "npm: build", "type": "shell", "command": "npm", "args": ["run", "deploy"], "options": {"cwd": "${workspaceFolder}/assets"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "dependsOn": ["npm: install"]}, {"label": "docker: build", "type": "shell", "command": "docker", "args": ["build", "-t", "phoenix-app:local", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "docker-compose: up", "type": "shell", "command": "docker-compose", "args": ["-f", "k8s/local/docker-compose.yml", "up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "k8s: deploy local", "type": "shell", "command": "./scripts/setup-local.sh", "options": {"cwd": "${workspaceFolder}/k8s/local"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}