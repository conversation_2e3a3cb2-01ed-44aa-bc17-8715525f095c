{
  // Elixir Language Server settings
  "elixirLS.dialyzerEnabled": true,
  "elixirLS.fetchDeps": true,
  "elixirLS.suggestSpecs": true,
  "elixirLS.signatureAfterComplete": true,
  "elixirLS.enableTestLenses": true,
  "elixirLS.mixEnv": "dev",
  "elixirLS.projectDir": "",
  // Editor settings
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true
  },
  "editor.rulers": [
    80,
    120
  ],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 120,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.trimAutoWhitespace": true,
  "editor.renderWhitespace": "boundary",
  // File associations
  "files.associations": {
    "*.ex": "elixir",
    "*.exs": "elixir",
    "*.eex": "phoenix-heex",
    "*.heex": "phoenix-heex",
    "*.leex": "phoenix-heex",
    "mix.lock": "elixir",
    "*.dockerfile": "dockerfile",
    "Dockerfile*": "dockerfile"
  },
  // File exclusions for search and file watcher
  "search.exclude": {
    "**/node_modules": true,
    "**/deps": true,
    "**/_build": true,
    "**/cover": true,
    "**/doc": true,
    "**/.elixir_ls": true,
    "**/tmp": true,
    "**/log": true
  },
  "files.watcherExclude": {
    "**/_build/**": true,
    "**/deps/**": true,
    "**/node_modules/**": true,
    "**/.elixir_ls/**": true,
    "**/tmp/**": true,
    "**/log/**": true,
    "**/cover/**": true
  },
  // Terminal settings
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.profiles.linux": {
    "bash": {
      "path": "/bin/bash",
      "args": [
        "-l"
      ]
    },
    "iex": {
      "path": "/usr/local/bin/iex",
      "args": [
        "-S",
        "mix"
      ]
    },
    "phoenix": {
      "path": "/bin/bash",
      "args": [
        "-l",
        "-c",
        "mix phx.server"
      ]
    }
  },
  // Language-specific settings
  "[elixir]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },
  "[phoenix-heex]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },
  "[javascript]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[yaml]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },
  "[dockerfile]": {
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true
  },
  // Git settings
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.showPushSuccessNotification": true,
  // Docker settings
  "docker.showStartPage": false,
  // Test Explorer settings
  "testExplorer.useNativeTesting": true,
  // Prettier settings
  "prettier.tabWidth": 2,
  "prettier.useTabs": false,
  "prettier.semi": true,
  "prettier.singleQuote": true,
  "prettier.trailingComma": "es5",
  // Tailwind CSS settings
  "tailwindCSS.includeLanguages": {
    "elixir": "html",
    "phoenix-heex": "html",
    "surface": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    "class[:]\\s*\"([^\"]*)"
  ],
  // Emmet settings
  "emmet.includeLanguages": {
    "phoenix-heex": "html",
    "surface": "html"
  },
  // Spell checker settings
  "cSpell.words": [
    "Ecto",
    "GenServer",
    "LiveView",
    "Oban",
    "Phoenix",
    "Plug",
    "Repo",
    "changeset",
    "defmodule",
    "defp",
    "deps",
    "ecto",
    "elixir",
    "genserver",
    "heex",
    "iex",
    "liveview",
    "phx",
    "pubsub",
    "telemetry"
  ],
  // Todo Tree settings
  "todo-tree.general.tags": [
    "BUG",
    "HACK",
    "FIXME",
    "TODO",
    "XXX",
    "[ ]",
    "[x]"
  ],
  // Better Comments settings
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    }
  ],
  // Phoenix-specific settings
  "phoenix.liveReload": true,
  "phoenix.enableTelemetry": true,
  // Workspace-specific settings
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": "vs-seti",
  "workbench.startupEditor": "readme",
  // Explorer settings
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  // Breadcrumbs
  "breadcrumbs.enabled": true,
  // Minimap
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 120,
  // IntelliSense
  "editor.suggestSelection": "first",
  "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue"
}