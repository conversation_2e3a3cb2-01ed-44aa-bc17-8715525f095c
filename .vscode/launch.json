{"version": "0.2.0", "configurations": [{"type": "mix_task", "name": "Phoenix Server", "request": "launch", "task": "phx.server", "taskArgs": [], "startApps": true, "projectDir": "${workspaceRoot}", "requireFiles": ["test/**/test_helper.exs", "test/**/*_test.exs"], "env": {"MIX_ENV": "dev"}, "preLaunchTask": "mix: deps.get"}, {"type": "mix_task", "name": "Phoenix Server (Production)", "request": "launch", "task": "phx.server", "taskArgs": [], "startApps": true, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "prod"}, "preLaunchTask": "mix: compile"}, {"type": "mix_task", "name": "Run Tests", "request": "launch", "task": "test", "taskArgs": [], "startApps": true, "projectDir": "${workspaceRoot}", "requireFiles": ["test/**/test_helper.exs", "test/**/*_test.exs"], "env": {"MIX_ENV": "test"}, "preLaunchTask": "mix: test.setup"}, {"type": "mix_task", "name": "Run Specific Test File", "request": "launch", "task": "test", "taskArgs": ["${file}"], "startApps": true, "projectDir": "${workspaceRoot}", "requireFiles": ["test/**/test_helper.exs", "test/**/*_test.exs"], "env": {"MIX_ENV": "test"}}, {"type": "mix_task", "name": "Run Tests with Coverage", "request": "launch", "task": "test", "taskArgs": ["--cover"], "startApps": true, "projectDir": "${workspaceRoot}", "requireFiles": ["test/**/test_helper.exs", "test/**/*_test.exs"], "env": {"MIX_ENV": "test"}}, {"type": "mix_task", "name": "IEx Console", "request": "launch", "task": "run", "taskArgs": ["--no-halt"], "startApps": true, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Database Migration", "request": "launch", "task": "ecto.migrate", "taskArgs": [], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Database Rollback", "request": "launch", "task": "ecto.rollback", "taskArgs": [], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Database Reset", "request": "launch", "task": "ecto.reset", "taskArgs": [], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Generate Migration", "request": "launch", "task": "ecto.gen.migration", "taskArgs": ["${input:migrationName}"], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Format Code", "request": "launch", "task": "format", "taskArgs": [], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "<PERSON>redo Analysis", "request": "launch", "task": "credo", "taskArgs": ["--strict"], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}, {"type": "mix_task", "name": "Dialyzer", "request": "launch", "task": "dialyzer", "taskArgs": [], "startApps": false, "projectDir": "${workspaceRoot}", "env": {"MIX_ENV": "dev"}}], "inputs": [{"id": "migrationName", "description": "Migration name", "default": "add_new_field", "type": "promptString"}]}