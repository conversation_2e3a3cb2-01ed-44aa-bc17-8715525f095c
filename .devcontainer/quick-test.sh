#!/bin/bash

# Quick Dev Container Test Script
# Run this to quickly identify the most common issues

set -e

echo "🔍 Quick Dev Container Diagnostics"
echo "=================================="

# Test 1: Configuration files
echo "1. Testing configuration files..."
if python3 -m json.tool .devcontainer/devcontainer.json > /dev/null 2>&1; then
    echo "   ✅ devcontainer.json is valid JSON"
else
    echo "   ❌ devcontainer.json has JSON syntax errors"
    exit 1
fi

cd .devcontainer
if docker compose config > /dev/null 2>&1; then
    echo "   ✅ docker-compose.yml is valid"
else
    echo "   ❌ docker-compose.yml has syntax errors"
    cd ..
    exit 1
fi
cd ..

# Test 2: Docker status
echo "2. Testing Docker..."
if docker info > /dev/null 2>&1; then
    echo "   ✅ Docker daemon is running"
else
    echo "   ❌ Docker daemon is not running"
    exit 1
fi

# Test 3: Port conflicts
echo "3. Checking ports..."
conflicts=0
for port in 4000 5432 6379; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo "   ⚠️  Port $port is in use"
        conflicts=$((conflicts + 1))
    fi
done

if [ $conflicts -eq 0 ]; then
    echo "   ✅ No port conflicts"
else
    echo "   ⚠️  Found $conflicts port conflicts"
fi

# Test 4: VS Code extension
echo "4. Checking VS Code..."
if command -v code > /dev/null 2>&1; then
    if code --list-extensions | grep -q "ms-vscode-remote.remote-containers"; then
        echo "   ✅ Dev Containers extension is installed"
    else
        echo "   ❌ Dev Containers extension is missing"
        echo "      Install with: code --install-extension ms-vscode-remote.remote-containers"
    fi
else
    echo "   ⚠️  VS Code CLI not available"
fi

echo
echo "🚀 Next steps:"
echo "1. If all tests passed: Try opening VS Code dev container"
echo "2. If issues found: Run ./.devcontainer/debug-devcontainer.sh for detailed diagnosis"
echo "3. For quick fixes: Run ./.devcontainer/quick-fix.sh"
echo
echo "To open in VS Code:"
echo "   code ."
echo "   Command Palette → 'Dev Containers: Reopen in Container'"
