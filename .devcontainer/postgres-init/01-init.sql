-- PostgreSQL initialization script for Phoenix development
-- This script runs when the PostgreSQL container is first created

-- Create additional databases if needed
-- CREATE DATABASE phoenix_test;

-- Create extensions that are commonly used in Phoenix applications
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create a read-only user for development tools
CREATE USER readonly_user WITH PASSWORD 'readonly';
GRANT CONNECT ON DATABASE phoenix_dev TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO readonly_user;

-- Set up some useful functions for development
CREATE OR REPLACE FUNCTION reset_sequence(table_name text, column_name text DEFAULT 'id')
RET<PERSON>NS void AS $$
DECLARE
    max_id integer;
    sequence_name text;
BEGIN
    -- Get the maximum ID from the table
    EXECUTE format('SELECT COALESCE(MAX(%I), 0) FROM %I', column_name, table_name) INTO max_id;
    
    -- Construct sequence name (assuming standard naming convention)
    sequence_name := table_name || '_' || column_name || '_seq';
    
    -- Reset the sequence
    EXECUTE format('SELECT setval(%L, %s)', sequence_name, max_id + 1);
END;
$$ LANGUAGE plpgsql;

-- Function to show table sizes (useful for development)
CREATE OR REPLACE FUNCTION table_sizes()
RETURNS TABLE(
    table_name text,
    row_count bigint,
    total_size text,
    index_size text,
    toast_size text,
    table_size text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename AS table_name,
        n_tup_ins - n_tup_del AS row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS total_size,
        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) AS index_size,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) AS toast_size,
        pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) AS table_size
    FROM pg_stat_user_tables
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- Set timezone for development
SET timezone = 'UTC';

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'Phoenix development database initialized successfully';
    RAISE NOTICE 'Available extensions: uuid-ossp, citext, pg_trgm, btree_gin, btree_gist';
    RAISE NOTICE 'Created readonly_user for development tools';
    RAISE NOTICE 'Added helper functions: reset_sequence(), table_sizes()';
END $$;
