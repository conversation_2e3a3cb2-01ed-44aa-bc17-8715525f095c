# Manual Dev Container Debugging Steps

## 🔍 Step-by-Step Diagnostic Commands

Run these commands **in order** to systematically identify the issue:

### **Step 1: Validate Configuration Files**

```bash
# Test JSON syntax
python3 -m json.tool .devcontainer/devcontainer.json > /dev/null && echo "✅ JSON valid" || echo "❌ JSON invalid"

# Test Docker Compose syntax
cd .devcontainer && docker compose config > /dev/null && echo "✅ Compose valid" || echo "❌ Compose invalid" && cd ..

# Check required files
ls -la .devcontainer/
```

**Expected Output:**
- ✅ JSON valid
- ✅ Compose valid
- Files: `devcontainer.json`, `docker-compose.yml`, `Dockerfile`, `post-create.sh`, `post-attach.sh`

---

### **Step 2: Test Docker Environment**

```bash
# Check Docker daemon
docker info

# Check Docker Compose
docker compose version

# Check system resources
docker system df

# Check for conflicting containers
docker ps -a | grep phoenix
```

**Expected Output:**
- Docker info shows running daemon
- Docker Compose version 2.x
- Sufficient disk space available
- No conflicting Phoenix containers

---

### **Step 3: Test Port Availability**

```bash
# Check critical ports
for port in 4000 5432 6379; do
  if lsof -i :$port > /dev/null 2>&1; then
    echo "❌ Port $port is in use: $(lsof -i :$port | tail -1 | awk '{print $1, $2}')"
  else
    echo "✅ Port $port is available"
  fi
done
```

**Expected Output:**
- ✅ All ports available
- If ports are in use, kill the processes:
  ```bash
  sudo lsof -ti:4000 | xargs kill -9  # Phoenix
  sudo lsof -ti:5432 | xargs kill -9  # PostgreSQL
  sudo lsof -ti:6379 | xargs kill -9  # Redis
  ```

---

### **Step 4: Test Docker Compose Services Independently**

```bash
cd .devcontainer

# Clean up first
docker compose down -v

# Test PostgreSQL
echo "Testing PostgreSQL..."
docker compose up -d postgres
sleep 10
docker compose exec postgres pg_isready -U postgres
echo "PostgreSQL status: $?"

# Test Redis
echo "Testing Redis..."
docker compose up -d redis
sleep 5
docker compose exec redis redis-cli ping
echo "Redis status: $?"

# Check logs if services fail
docker compose logs postgres
docker compose logs redis
```

**Expected Output:**
- PostgreSQL: `accepting connections`
- Redis: `PONG`
- No error messages in logs

---

### **Step 5: Test Container Build**

```bash
cd .devcontainer

# Build the development container
echo "Building phoenix-dev container..."
docker compose build phoenix-dev --progress=plain

# Check if image was created
docker images | grep phoenix-dev
```

**Expected Output:**
- Build completes without errors
- Image appears in `docker images` list
- Image size should be reasonable (< 2GB)

**If build fails:**
```bash
# Clear Docker cache and retry
docker builder prune -f
docker compose build phoenix-dev --no-cache --progress=plain
```

---

### **Step 6: Test Full Stack Startup**

```bash
cd .devcontainer

# Start all services
docker compose up -d

# Wait for startup
sleep 15

# Check service health
echo "=== Service Status ==="
docker compose ps

echo "=== PostgreSQL Health ==="
docker compose exec postgres pg_isready -U postgres

echo "=== Redis Health ==="
docker compose exec redis redis-cli ping

echo "=== Phoenix Container ==="
docker compose exec phoenix-dev echo "Container is running"

echo "=== Elixir Version ==="
docker compose exec phoenix-dev elixir --version
```

**Expected Output:**
- All services show "Up" status
- PostgreSQL: `accepting connections`
- Redis: `PONG`
- Phoenix container: `Container is running`
- Elixir version displayed

---

### **Step 7: Test VS Code Dev Container Extension**

```bash
# Check VS Code CLI
code --version

# Check Dev Containers extension
code --list-extensions | grep ms-vscode-remote.remote-containers

# Check extension version
code --list-extensions --show-versions | grep ms-vscode-remote.remote-containers
```

**Expected Output:**
- VS Code version displayed
- Extension ID: `ms-vscode-remote.remote-containers`
- Extension version (latest is recommended)

**If extension is missing:**
```bash
code --install-extension ms-vscode-remote.remote-containers
```

---

## 🔧 VS Code Dev Container Debugging

### **Access VS Code Logs**

1. **Open Developer Tools:**
   - Command Palette (`Cmd/Ctrl + Shift + P`)
   - Type: `Developer: Toggle Developer Tools`
   - Check **Console** tab for errors

2. **View Dev Container Logs:**
   - Command Palette → `Dev Containers: Show Container Log`
   - Look for specific error messages

3. **Check Output Panel:**
   - View → Output
   - Select "Dev Containers" from dropdown
   - Look for connection errors

### **Common VS Code Error Messages & Solutions**

| Error Message | Solution |
|---------------|----------|
| "Failed to connect" | Check Docker daemon is running |
| "Port already in use" | Kill conflicting processes |
| "Container failed to start" | Check Docker Compose logs |
| "Extension host terminated" | Reload VS Code window |
| "Docker not found" | Check Docker installation |

### **VS Code Debugging Commands**

```bash
# Reset VS Code dev container cache
rm -rf ~/.vscode/extensions/ms-vscode-remote.remote-containers-*/dist/dev-containers-cli/cache

# Check VS Code settings
code --list-extensions --show-versions | grep remote

# Test VS Code Docker integration
code --version && docker --version
```

---

## 🚨 Manual Container Connection

If VS Code fails, connect manually:

```bash
# Start services
cd .devcontainer
docker compose up -d

# Connect to running container
docker compose exec phoenix-dev /bin/bash

# Inside container, test Elixir
elixir --version
mix --version

# Test database connection
mix ecto.create
```

---

## 🔄 Reset Everything

If all else fails:

```bash
# Stop all containers
docker stop $(docker ps -aq)

# Remove all containers
docker rm $(docker ps -aq)

# Clean Docker system
docker system prune -a --volumes

# Remove dev container cache
rm -rf ~/.vscode/extensions/ms-vscode-remote.remote-containers-*/dist/dev-containers-cli/cache

# Restart Docker Desktop
# Then try VS Code dev container again
```

---

## 📊 Success Indicators

You'll know it's working when:

✅ **Configuration files validate without errors**
✅ **Docker services start and respond to health checks**
✅ **Container builds successfully**
✅ **VS Code opens the dev container without errors**
✅ **Terminal shows custom prompt with Elixir info**
✅ **Extensions load properly**
✅ **Database and Redis connections work**

---

## 🆘 Getting Specific Error Information

Run the comprehensive diagnostic:
```bash
./.devcontainer/debug-devcontainer.sh
```

This will test each component systematically and provide detailed output for troubleshooting.
