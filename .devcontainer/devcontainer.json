{"name": "Phoenix Elixir Development", "dockerComposeFile": "docker-compose.yml", "service": "phoenix-dev", "workspaceFolder": "/workspace", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {"version": "latest", "helm": "latest", "minikube": "none"}}, "customizations": {"vscode": {"extensions": ["jakebecker.elixir-ls", "phoenixframework.phoenix", "msaraiva.elixir-formatter", "ms-ossdata.vscode-postgresql", "cweijan.vscode-redis-client", "ms-vscode.vscode-json", "eamodio.gitlens", "github.vscode-pull-request-github", "ms-azuretools.vscode-docker", "ms-kubernetes-tools.vscode-kubernetes-tools", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "ms-vscode.vscode-yaml", "redhat.vscode-xml", "ms-vscode.hexeditor", "streetsidesoftware.code-spell-checker", "hbenl.vscode-test-explorer", "ms-vscode.test-adapter-converter", "ms-vscode.vscode-todo-highlight", "gruntfuggly.todo-tree", "aaron-bond.better-comments"], "settings": {"elixirLS.dialyzerEnabled": true, "elixirLS.fetchDeps": true, "elixirLS.suggestSpecs": true, "elixirLS.signatureAfterComplete": true, "elixirLS.enableTestLenses": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.rulers": [80, 120], "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 120, "files.associations": {"*.ex": "elixir", "*.exs": "elixir", "*.eex": "phoenix-heex", "*.heex": "phoenix-heex", "*.leex": "phoenix-heex", "mix.lock": "elixir"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.profiles.linux": {"bash": {"path": "/bin/bash", "args": ["-l"]}, "iex": {"path": "/usr/local/bin/iex", "args": ["-S", "mix"]}}, "search.exclude": {"**/node_modules": true, "**/deps": true, "**/_build": true, "**/cover": true, "**/doc": true, "**/.elixir_ls": true}, "files.watcherExclude": {"**/_build/**": true, "**/deps/**": true, "**/node_modules/**": true, "**/.elixir_ls/**": true}, "postgresql.connections": [{"name": "Local Development", "host": "postgres", "port": 5432, "username": "postgres", "password": "postgres", "database": "phoenix_dev"}], "redis-client.connections": [{"name": "Local Development", "host": "redis", "port": 6379, "database": 0}], "git.enableSmartCommit": true, "git.confirmSync": false, "git.autofetch": true, "docker.showStartPage": false, "vs-kubernetes": {"vs-kubernetes.crd-code-completion": "enabled", "vs-kubernetes.kubectl-path.linux": "/usr/local/bin/kubectl"}}}}, "forwardPorts": [4000, 4001, 9090], "portsAttributes": {"4000": {"label": "Phoenix App", "onAutoForward": "notify"}, "4001": {"label": "LiveReload", "onAutoForward": "silent"}, "9090": {"label": "Metrics", "onAutoForward": "silent"}}, "postCreateCommand": ".devcontainer/post-create.sh", "postAttachCommand": ".devcontainer/post-attach.sh", "mounts": ["source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"], "containerEnv": {"MIX_ENV": "dev", "PHX_HOST": "localhost", "PORT": "4000", "DATABASE_URL": "ecto://postgres:postgres@postgres:5432/phoenix_dev", "REDIS_HOST": "redis", "REDIS_PORT": "6379", "REDIS_DATABASE": "0", "LOG_LEVEL": "debug", "ENABLE_TELEMETRY": "true", "LIVE_VIEW_SIGNING_SALT": "phoenix_live_view_dev", "SECRET_KEY_BASE": "your-secret-key-base-for-local-development-change-this-in-production", "GUARDIAN_SECRET_KEY": "your-guardian-secret-key-for-local-development", "JWT_SECRET": "your-jwt-secret-for-local-development", "ENCRYPTION_KEY": "your-encryption-key-for-local-development-32-chars"}, "remoteUser": "vscode", "shutdownAction": "stopCompose"}