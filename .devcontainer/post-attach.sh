#!/bin/bash

# Post-attach script for Phoenix Elixir Dev Container
# This script runs every time you attach to the container

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check service health
check_services() {
    log_info "Checking service health..."
    
    # Check PostgreSQL
    if pg_isready -h postgres -p 5432 -U postgres >/dev/null 2>&1; then
        log_success "✓ PostgreSQL is healthy"
    else
        log_warning "⚠ PostgreSQL is not responding"
    fi
    
    # Check Redis
    if redis-cli -h redis ping >/dev/null 2>&1; then
        log_success "✓ Redis is healthy"
    else
        log_warning "⚠ Redis is not responding"
    fi
}

# Display development status
show_status() {
    echo
    log_info "🔥 Phoenix Elixir Development Environment"
    echo "=========================================="
    
    # Show Elixir version
    echo "Elixir: $(elixir --version | head -1)"
    echo "Erlang: $(erl -eval 'erlang:display(erlang:system_info(otp_release)), halt().' -noshell)"
    
    # Show Node.js version
    echo "Node.js: $(node --version)"
    echo "npm: $(npm --version)"
    
    # Show Phoenix version if available
    if command -v mix >/dev/null 2>&1; then
        if mix archive | grep -q phx_new; then
            echo "Phoenix: $(mix phx.new --version 2>/dev/null || echo 'installed')"
        fi
    fi
    
    echo
    check_services
    
    echo
    log_info "🚀 Ready for development!"
    echo "  • Start Phoenix: mix phx.server"
    echo "  • Open console: iex -S mix"
    echo "  • Run tests: mix test"
    echo "  • Access app: http://localhost:4000"
}

# Check if this is a Phoenix project
check_phoenix_project() {
    if [ -f "mix.exs" ]; then
        # Check if dependencies are installed
        if [ ! -d "deps" ] || [ ! -d "_build" ]; then
            log_warning "Dependencies not found. Run 'mix deps.get' to install them."
        fi
        
        # Check if database exists
        if ! mix ecto.create --quiet >/dev/null 2>&1; then
            log_info "Database exists or connection failed"
        fi
    else
        log_info "No mix.exs found. This might not be a Phoenix project."
        log_info "To create a new Phoenix project: mix phx.new my_app"
    fi
}

# Set up terminal prompt
setup_prompt() {
    # Add Phoenix project info to prompt if starship is available
    if command -v starship >/dev/null 2>&1; then
        export STARSHIP_CONFIG=/workspace/.devcontainer/starship.toml
        
        # Create starship config if it doesn't exist
        if [ ! -f "$STARSHIP_CONFIG" ]; then
            cat > "$STARSHIP_CONFIG" << 'EOF'
format = """
[](#9A348E)\
$os\
$username\
[](bg:#DA627D fg:#9A348E)\
$directory\
[](fg:#DA627D bg:#FCA17D)\
$git_branch\
$git_status\
[](fg:#FCA17D bg:#86BBD8)\
$elixir\
$nodejs\
[](fg:#86BBD8 bg:#06969A)\
$docker_context\
[](fg:#06969A bg:#33658A)\
$time\
[ ](fg:#33658A)\
"""

[os]
disabled = false
style = "bg:#9A348E"

[username]
show_always = true
style_user = "bg:#9A348E"
style_root = "bg:#9A348E"
format = '[$user ]($style)'
disabled = false

[directory]
style = "bg:#DA627D"
format = "[ $path ]($style)"
truncation_length = 3
truncation_symbol = "…/"

[git_branch]
symbol = ""
style = "bg:#FCA17D"
format = '[ $symbol $branch ]($style)'

[git_status]
style = "bg:#FCA17D"
format = '[$all_status$ahead_behind ]($style)'

[elixir]
symbol = ""
style = "bg:#86BBD8"
format = '[ $symbol ($version) ]($style)'

[nodejs]
symbol = ""
style = "bg:#86BBD8"
format = '[ $symbol ($version) ]($style)'

[docker_context]
symbol = ""
style = "bg:#06969A"
format = '[ $symbol $context ]($style)'

[time]
disabled = false
time_format = "%R"
style = "bg:#33658A"
format = '[ ♥ $time ]($style)'
EOF
        fi
    fi
}

# Main execution
main() {
    setup_prompt
    show_status
    check_phoenix_project
    
    # Source the updated bashrc to get aliases
    if [ -f ~/.bashrc ]; then
        source ~/.bashrc
    fi
}

# Run main function
main "$@"
