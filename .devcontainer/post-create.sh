#!/bin/bash

# Post-create script for Phoenix Elixir Dev Container
# This script runs after the container is created

set -e

echo "🚀 Setting up Phoenix Elixir development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for database and cache services to be ready..."
    
    # Wait for PostgreSQL
    until pg_isready -h postgres -p 5432 -U postgres; do
        echo "Waiting for PostgreSQL..."
        sleep 2
    done
    log_success "PostgreSQL is ready"
    
    # Wait for Redis
    until redis-cli -h redis ping; do
        echo "Waiting for Redis..."
        sleep 2
    done
    log_success "Redis is ready"
}

# Install or update dependencies
install_dependencies() {
    log_info "Installing/updating Elixir dependencies..."
    
    # Check if mix.exs exists
    if [ -f "mix.exs" ]; then
        # Get dependencies
        mix deps.get
        
        # Compile dependencies
        mix deps.compile
        
        log_success "Elixir dependencies installed"
    else
        log_warning "No mix.exs found. Skipping Elixir dependency installation."
    fi
    
    # Install Node.js dependencies if package.json exists
    if [ -f "assets/package.json" ]; then
        log_info "Installing Node.js dependencies..."
        cd assets
        npm install
        cd ..
        log_success "Node.js dependencies installed"
    else
        log_warning "No assets/package.json found. Skipping Node.js dependency installation."
    fi
}

# Set up database
setup_database() {
    log_info "Setting up database..."
    
    if [ -f "mix.exs" ]; then
        # Create database if it doesn't exist
        mix ecto.create || log_warning "Database might already exist"
        
        # Run migrations if they exist
        if [ -d "priv/repo/migrations" ] && [ "$(ls -A priv/repo/migrations)" ]; then
            mix ecto.migrate
            log_success "Database migrations completed"
        else
            log_info "No migrations found"
        fi
        
        # Run seeds if they exist
        if [ -f "priv/repo/seeds.exs" ]; then
            mix run priv/repo/seeds.exs
            log_success "Database seeded"
        else
            log_info "No seeds file found"
        fi
    else
        log_warning "No mix.exs found. Skipping database setup."
    fi
}

# Set up Git configuration
setup_git() {
    log_info "Setting up Git configuration..."
    
    # Set up safe directory (for mounted volumes)
    git config --global --add safe.directory /workspace
    
    # Set up default Git configuration if not already set
    if [ -z "$(git config --global user.name)" ]; then
        log_info "Git user.name not set. You may want to configure it:"
        log_info "git config --global user.name 'Your Name'"
    fi
    
    if [ -z "$(git config --global user.email)" ]; then
        log_info "Git user.email not set. You may want to configure it:"
        log_info "git config --global user.email '<EMAIL>'"
    fi
    
    log_success "Git configuration completed"
}

# Set up shell environment
setup_shell() {
    log_info "Setting up shell environment..."
    
    # Create useful aliases and functions
    cat >> ~/.bashrc << 'EOF'

# Phoenix development aliases
alias phx.server="mix phx.server"
alias phx.routes="mix phx.routes"
alias phx.digest="mix phx.digest"
alias phx.new="mix phx.new"

# Database aliases
alias db.create="mix ecto.create"
alias db.migrate="mix ecto.migrate"
alias db.rollback="mix ecto.rollback"
alias db.seed="mix run priv/repo/seeds.exs"
alias db.reset="mix ecto.reset"
alias db.setup="mix ecto.setup"

# Testing aliases
alias test.watch="mix test.watch"
alias test.coverage="mix test --cover"

# Development helpers
alias deps.clean="mix deps.clean --all"
alias deps.update="mix deps.update --all"
alias format="mix format"
alias credo="mix credo"
alias dialyzer="mix dialyzer"

# Kubernetes helpers (for integration with local k8s)
alias k="kubectl"
alias kns="kubectl config set-context --current --namespace"
alias kgp="kubectl get pods"
alias kgs="kubectl get services"
alias kgi="kubectl get ingress"

# Docker helpers
alias dc="docker-compose"
alias dcu="docker-compose up"
alias dcd="docker-compose down"
alias dcl="docker-compose logs"

# Utility functions
function db_console() {
    psql -h postgres -U postgres -d phoenix_dev
}

function redis_console() {
    redis-cli -h redis
}

function phoenix_console() {
    iex -S mix
}

function watch_logs() {
    tail -f log/dev.log
}

EOF

    log_success "Shell environment configured"
}

# Create development directories
create_directories() {
    log_info "Creating development directories..."
    
    # Create common Phoenix directories if they don't exist
    mkdir -p tmp
    mkdir -p log
    mkdir -p priv/static
    mkdir -p test/support
    
    log_success "Development directories created"
}

# Set up VS Code workspace settings
setup_vscode() {
    log_info "Setting up VS Code workspace..."
    
    # Create .vscode directory if it doesn't exist
    mkdir -p .vscode
    
    # Create launch.json for debugging
    cat > .vscode/launch.json << 'EOF'
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "mix_task",
            "name": "mix (Default task)",
            "request": "launch",
            "task": "phx.server",
            "taskArgs": [],
            "startApps": true,
            "projectDir": "${workspaceRoot}",
            "requireFiles": [
                "test/**/test_helper.exs",
                "test/**/*_test.exs"
            ]
        },
        {
            "type": "mix_task",
            "name": "mix test",
            "request": "launch",
            "task": "test",
            "taskArgs": [],
            "startApps": true,
            "projectDir": "${workspaceRoot}",
            "requireFiles": [
                "test/**/test_helper.exs",
                "test/**/*_test.exs"
            ]
        }
    ]
}
EOF

    # Create tasks.json for common tasks
    cat > .vscode/tasks.json << 'EOF'
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "mix phx.server",
            "type": "shell",
            "command": "mix",
            "args": ["phx.server"],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "mix test",
            "type": "shell",
            "command": "mix",
            "args": ["test"],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "mix deps.get",
            "type": "shell",
            "command": "mix",
            "args": ["deps.get"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": []
        }
    ]
}
EOF

    log_success "VS Code workspace configured"
}

# Display helpful information
show_info() {
    log_success "Phoenix Elixir development environment setup complete!"
    echo
    log_info "🎯 Quick Start:"
    echo "  • Phoenix server: mix phx.server (or 'phx.server' alias)"
    echo "  • Interactive console: iex -S mix (or 'phoenix_console' function)"
    echo "  • Run tests: mix test (or 'test' alias)"
    echo "  • Database console: psql -h postgres -U postgres -d phoenix_dev (or 'db_console' function)"
    echo "  • Redis console: redis-cli -h redis (or 'redis_console' function)"
    echo
    log_info "🔗 Service URLs:"
    echo "  • Phoenix App: http://localhost:4000"
    echo "  • pgAdmin: http://localhost:8080 (<EMAIL> / admin)"
    echo "  • Redis Commander: http://localhost:8081 (admin / admin)"
    echo "  • Mailhog: http://localhost:8025"
    echo
    log_info "🛠️ Development Tools:"
    echo "  • All Elixir/Phoenix tools are pre-installed"
    echo "  • Database and Redis clients are available"
    echo "  • Docker and Kubernetes tools are installed"
    echo "  • VS Code is configured with Elixir extensions"
    echo
    log_info "📚 Useful Commands:"
    echo "  • View all aliases: alias | grep -E '(phx|db|test)'"
    echo "  • Check service status: docker-compose ps"
    echo "  • View logs: docker-compose logs -f"
}

# Main execution
main() {
    wait_for_services
    install_dependencies
    setup_database
    setup_git
    setup_shell
    create_directories
    setup_vscode
    show_info
}

# Run main function
main "$@"
