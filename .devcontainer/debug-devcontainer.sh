#!/bin/bash

# Comprehensive Dev Container Debugging Script
# This script systematically tests each component to identify connection issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
log_check() { echo -e "${CYAN}[CHECK]${NC} $1"; }

# Header
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║           Dev Container Connection Diagnostics               ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Step 1: Validate Configuration Files
validate_configs() {
    log_step "1. Validating Configuration Files"
    echo "===================================="
    
    # Test devcontainer.json
    log_check "devcontainer.json syntax..."
    if python3 -m json.tool .devcontainer/devcontainer.json > /dev/null 2>&1; then
        log_success "devcontainer.json is valid JSON"
    else
        log_error "devcontainer.json has syntax errors:"
        python3 -m json.tool .devcontainer/devcontainer.json
        return 1
    fi
    
    # Test docker-compose.yml
    log_check "docker-compose.yml syntax..."
    cd .devcontainer
    if docker compose config > /dev/null 2>&1; then
        log_success "docker-compose.yml is valid"
    else
        log_error "docker-compose.yml has syntax errors:"
        docker compose config
        cd ..
        return 1
    fi
    cd ..
    
    # Check required files exist
    local required_files=("Dockerfile" "post-create.sh" "post-attach.sh")
    for file in "${required_files[@]}"; do
        if [ -f ".devcontainer/$file" ]; then
            log_success "$file exists"
            if [[ "$file" == *.sh ]]; then
                if [ -x ".devcontainer/$file" ]; then
                    log_success "$file is executable"
                else
                    log_warning "$file is not executable, fixing..."
                    chmod +x ".devcontainer/$file"
                fi
            fi
        else
            log_error "$file is missing"
        fi
    done
    
    echo
}

# Step 2: Test Docker Environment
test_docker() {
    log_step "2. Testing Docker Environment"
    echo "==============================="
    
    # Check Docker daemon
    log_check "Docker daemon status..."
    if docker info > /dev/null 2>&1; then
        log_success "Docker daemon is running"
        DOCKER_VERSION=$(docker --version)
        log_info "Docker version: $DOCKER_VERSION"
    else
        log_error "Docker daemon is not running or accessible"
        return 1
    fi
    
    # Check Docker Compose
    log_check "Docker Compose..."
    if docker compose version > /dev/null 2>&1; then
        COMPOSE_VERSION=$(docker compose version)
        log_success "Docker Compose available: $COMPOSE_VERSION"
    else
        log_error "Docker Compose not available"
        return 1
    fi
    
    # Check Docker resources
    log_check "Docker system resources..."
    docker system df
    
    # Check for conflicting containers
    log_check "Existing containers..."
    EXISTING=$(docker ps -a --filter "name=phoenix" --format "table {{.Names}}\t{{.Status}}")
    if [ -n "$EXISTING" ]; then
        log_warning "Found existing Phoenix containers:"
        echo "$EXISTING"
    else
        log_success "No conflicting containers found"
    fi
    
    echo
}

# Step 3: Test Port Availability
test_ports() {
    log_step "3. Testing Port Availability"
    echo "============================="
    
    local ports=(4000 4001 5432 6379 8080 8081 8025 9090)
    local conflicts=0
    
    for port in "${ports[@]}"; do
        log_check "Port $port..."
        if lsof -i :$port > /dev/null 2>&1; then
            PROCESS=$(lsof -i :$port | tail -1 | awk '{print $1, $2}')
            log_warning "Port $port is in use by: $PROCESS"
            conflicts=$((conflicts + 1))
        else
            log_success "Port $port is available"
        fi
    done
    
    if [ $conflicts -gt 0 ]; then
        log_warning "Found $conflicts port conflicts. You may need to stop conflicting services."
        echo "To kill processes: sudo lsof -ti:<port> | xargs kill -9"
    fi
    
    echo
}

# Step 4: Test Docker Compose Services Independently
test_compose_services() {
    log_step "4. Testing Docker Compose Services"
    echo "==================================="
    
    cd .devcontainer
    
    # Clean up any existing containers
    log_check "Cleaning up existing containers..."
    docker compose down -v > /dev/null 2>&1 || true
    
    # Test PostgreSQL service
    log_check "Testing PostgreSQL service..."
    if docker compose up -d postgres; then
        log_info "Waiting for PostgreSQL to start..."
        sleep 10
        
        if docker compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
            log_success "PostgreSQL is healthy"
            
            # Test database creation
            if docker compose exec postgres psql -U postgres -c "SELECT version();" > /dev/null 2>&1; then
                log_success "PostgreSQL connection works"
            else
                log_error "PostgreSQL connection failed"
            fi
        else
            log_error "PostgreSQL failed to start properly"
            log_info "PostgreSQL logs:"
            docker compose logs postgres
        fi
    else
        log_error "Failed to start PostgreSQL service"
    fi
    
    # Test Redis service
    log_check "Testing Redis service..."
    if docker compose up -d redis; then
        log_info "Waiting for Redis to start..."
        sleep 5
        
        if docker compose exec redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
            log_success "Redis is healthy"
        else
            log_error "Redis failed to start properly"
            log_info "Redis logs:"
            docker compose logs redis
        fi
    else
        log_error "Failed to start Redis service"
    fi
    
    # Test network connectivity between services
    log_check "Testing service connectivity..."
    if docker compose exec postgres ping -c 1 redis > /dev/null 2>&1; then
        log_success "PostgreSQL can reach Redis"
    else
        log_warning "PostgreSQL cannot reach Redis (this may be normal)"
    fi
    
    cd ..
    echo
}

# Step 5: Test Container Build
test_container_build() {
    log_step "5. Testing Container Build"
    echo "============================"
    
    cd .devcontainer
    
    log_check "Building phoenix-dev container..."
    if docker compose build phoenix-dev --progress=plain; then
        log_success "Container built successfully"
        
        # Check image size
        IMAGE_SIZE=$(docker images phoenix-dev --format "table {{.Size}}" | tail -1)
        log_info "Container image size: $IMAGE_SIZE"
    else
        log_error "Container build failed"
        cd ..
        return 1
    fi
    
    cd ..
    echo
}

# Step 6: Test Full Stack Startup
test_full_stack() {
    log_step "6. Testing Full Stack Startup"
    echo "==============================="
    
    cd .devcontainer
    
    log_check "Starting all services..."
    if docker compose up -d; then
        log_info "Waiting for all services to start..."
        sleep 15
        
        # Check service health
        log_check "Checking service health..."
        
        # PostgreSQL
        if docker compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
            log_success "✓ PostgreSQL is healthy"
        else
            log_error "✗ PostgreSQL is not healthy"
        fi
        
        # Redis
        if docker compose exec redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
            log_success "✓ Redis is healthy"
        else
            log_error "✗ Redis is not healthy"
        fi
        
        # Phoenix container
        if docker compose exec phoenix-dev echo "Container is running" > /dev/null 2>&1; then
            log_success "✓ Phoenix dev container is running"
            
            # Test Elixir installation
            if docker compose exec phoenix-dev elixir --version > /dev/null 2>&1; then
                ELIXIR_VERSION=$(docker compose exec phoenix-dev elixir --version | head -1)
                log_success "✓ Elixir is available: $ELIXIR_VERSION"
            else
                log_error "✗ Elixir is not available in container"
            fi
        else
            log_error "✗ Phoenix dev container is not running"
        fi
        
        # Show container status
        log_info "Container status:"
        docker compose ps
        
    else
        log_error "Failed to start services"
    fi
    
    cd ..
    echo
}

# Step 7: Test VS Code Dev Container Extension
test_vscode_extension() {
    log_step "7. Testing VS Code Dev Container Extension"
    echo "=========================================="
    
    # Check if VS Code is available
    if command -v code > /dev/null 2>&1; then
        log_success "VS Code CLI is available"
        
        # Check Dev Containers extension
        if code --list-extensions | grep -q "ms-vscode-remote.remote-containers"; then
            log_success "Dev Containers extension is installed"
            
            # Get extension version
            EXT_VERSION=$(code --list-extensions --show-versions | grep "ms-vscode-remote.remote-containers" | cut -d'@' -f2)
            log_info "Extension version: $EXT_VERSION"
        else
            log_error "Dev Containers extension is not installed"
            log_info "Install with: code --install-extension ms-vscode-remote.remote-containers"
        fi
    else
        log_warning "VS Code CLI not available (this is optional)"
    fi
    
    # Check VS Code dev container cache
    CACHE_DIR="$HOME/.vscode/extensions/ms-vscode-remote.remote-containers-*/dist/dev-containers-cli/cache"
    if ls $CACHE_DIR > /dev/null 2>&1; then
        log_info "Dev container cache exists"
        CACHE_SIZE=$(du -sh $CACHE_DIR 2>/dev/null | cut -f1 || echo "unknown")
        log_info "Cache size: $CACHE_SIZE"
    else
        log_info "No dev container cache found (this is normal for first run)"
    fi
    
    echo
}

# Step 8: Generate VS Code Debugging Commands
generate_vscode_debug() {
    log_step "8. VS Code Debugging Commands"
    echo "==============================="
    
    echo -e "${YELLOW}To debug VS Code dev container issues:${NC}"
    echo
    echo "1. Open VS Code Developer Tools:"
    echo "   • Command Palette → 'Developer: Toggle Developer Tools'"
    echo "   • Check Console tab for errors"
    echo
    echo "2. Check VS Code dev container logs:"
    echo "   • Command Palette → 'Dev Containers: Show Container Log'"
    echo
    echo "3. Try rebuilding the container:"
    echo "   • Command Palette → 'Dev Containers: Rebuild Container'"
    echo "   • Or: 'Dev Containers: Rebuild Container Without Cache'"
    echo
    echo "4. Reset VS Code window:"
    echo "   • Command Palette → 'Developer: Reload Window'"
    echo
    echo "5. Check VS Code settings:"
    echo "   • Open Settings → Search for 'dev containers'"
    echo "   • Ensure 'Dev Containers: Docker Path' is correct"
    echo
    
    echo -e "${YELLOW}Manual container attachment (if VS Code fails):${NC}"
    echo "   docker compose -f .devcontainer/docker-compose.yml exec phoenix-dev /bin/bash"
    echo
}

# Step 9: Cleanup and Summary
cleanup_and_summary() {
    log_step "9. Cleanup and Summary"
    echo "======================="
    
    cd .devcontainer
    
    log_check "Stopping test containers..."
    docker compose down -v > /dev/null 2>&1 || true
    
    cd ..
    
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    Diagnostic Summary                        ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    log_info "Next steps based on results:"
    echo "1. If all tests passed: Try VS Code dev container again"
    echo "2. If Docker issues: Restart Docker Desktop and retry"
    echo "3. If port conflicts: Kill conflicting processes"
    echo "4. If build issues: Clear Docker cache and rebuild"
    echo "5. If VS Code issues: Reinstall Dev Containers extension"
    echo
    
    log_info "Quick fixes to try:"
    echo "• ./.devcontainer/quick-fix.sh"
    echo "• docker system prune -f"
    echo "• Restart Docker Desktop"
    echo "• Restart VS Code"
    echo
}

# Main execution
main() {
    validate_configs || exit 1
    test_docker || exit 1
    test_ports
    test_compose_services
    test_container_build
    test_full_stack
    test_vscode_extension
    generate_vscode_debug
    cleanup_and_summary
}

# Parse arguments
case "${1:-}" in
    --help|-h)
        echo "Dev Container Connection Diagnostics"
        echo "Usage: $0 [--help]"
        echo
        echo "This script systematically tests each component to identify dev container connection issues."
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
