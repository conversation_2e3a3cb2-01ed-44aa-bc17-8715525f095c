#!/bin/bash

# Quick Fix Script for Phoenix Dev Container Issues
# This script provides immediate solutions for common problems

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo -e "${BLUE}Phoenix Dev Container Quick Fix${NC}"
echo "==============================="

# Function to fix common issues
fix_permissions() {
    log_info "Fixing script permissions..."
    chmod +x .devcontainer/*.sh 2>/dev/null || true
    log_success "Script permissions fixed"
}

clean_docker() {
    log_info "Cleaning Docker resources..."
    docker system prune -f
    docker volume prune -f
    log_success "Docker cleanup completed"
}

kill_conflicting_ports() {
    log_info "Killing processes on conflicting ports..."
    for port in 4000 4001 5432 6379 8080 8081 8025 9090; do
        if lsof -ti:$port >/dev/null 2>&1; then
            log_warning "Killing process on port $port"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
        fi
    done
    log_success "Port conflicts resolved"
}

rebuild_container() {
    log_info "Rebuilding dev container..."
    cd .devcontainer
    docker compose down -v
    docker compose build --no-cache
    cd ..
    log_success "Container rebuilt"
}

reset_vscode() {
    log_info "Resetting VS Code dev container state..."
    # Remove VS Code dev container cache
    rm -rf ~/.vscode/extensions/ms-vscode-remote.remote-containers-*/dist/dev-containers-cli/cache 2>/dev/null || true
    log_success "VS Code state reset"
}

# Menu
echo
echo "Select a fix to apply:"
echo "1. Fix script permissions"
echo "2. Clean Docker resources"
echo "3. Kill conflicting port processes"
echo "4. Rebuild container (no cache)"
echo "5. Reset VS Code dev container state"
echo "6. Apply all fixes (recommended)"
echo "7. Exit"
echo

read -p "Enter your choice (1-7): " choice

case $choice in
    1) fix_permissions ;;
    2) clean_docker ;;
    3) kill_conflicting_ports ;;
    4) rebuild_container ;;
    5) reset_vscode ;;
    6) 
        log_info "Applying all fixes..."
        fix_permissions
        kill_conflicting_ports
        clean_docker
        reset_vscode
        rebuild_container
        log_success "All fixes applied!"
        ;;
    7) 
        log_info "Exiting..."
        exit 0
        ;;
    *)
        log_error "Invalid choice"
        exit 1
        ;;
esac

echo
log_success "Fix completed! Try opening the dev container again."
echo
echo "Next steps:"
echo "1. Open VS Code"
echo "2. Command Palette (Cmd/Ctrl + Shift + P)"
echo "3. 'Dev Containers: Reopen in Container'"
