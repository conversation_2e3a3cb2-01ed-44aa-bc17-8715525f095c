# Phoenix Elixir Development Container
FROM elixir:1.15-alpine

# Install system dependencies
RUN apk add --no-cache \
    # Build tools
    build-base \
    git \
    curl \
    wget \
    bash \
    zsh \
    fish \
    # Node.js for asset compilation
    nodejs \
    npm \
    # Database clients
    postgresql-client \
    postgresql-dev \
    # Redis client
    redis \
    # Development tools
    vim \
    nano \
    htop \
    tree \
    jq \
    yq \
    # Network tools
    netcat-openbsd \
    telnet \
    # SSL/TLS tools
    openssl \
    ca-certificates \
    # Python for some build processes
    python3 \
    python3-dev \
    py3-pip \
    # Additional utilities
    sudo \
    shadow \
    # Image processing (if needed for Phoenix)
    imagemagick \
    # PDF processing (if needed)
    # wkhtmltopdf \
    && rm -rf /var/cache/apk/*

# Install hex and rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Install Phoenix archive
RUN mix archive.install hex phx_new --force

# Install LiveBook for interactive development (optional)
RUN mix escript.install hex livebook --force

# Create a non-root user
ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=$USER_UID

RUN addgroup -g $USER_GID $USERNAME \
    && adduser -u $USER_UID -G $USERNAME -s /bin/bash -D $USERNAME \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

# Install additional tools as vscode user
USER $USERNAME

# Install global npm packages
RUN npm install -g \
    @tailwindcss/cli \
    esbuild \
    sass \
    postcss \
    autoprefixer

# Set up shell configuration
RUN echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc \
    && echo 'export MIX_ENV=dev' >> ~/.bashrc \
    && echo 'export ERL_AFLAGS="-kernel shell_history enabled"' >> ~/.bashrc \
    && echo 'alias ll="ls -la"' >> ~/.bashrc \
    && echo 'alias la="ls -A"' >> ~/.bashrc \
    && echo 'alias l="ls -CF"' >> ~/.bashrc \
    && echo 'alias iex="iex -S mix"' >> ~/.bashrc \
    && echo 'alias phx="mix phx.server"' >> ~/.bashrc \
    && echo 'alias test="mix test"' >> ~/.bashrc \
    && echo 'alias deps="mix deps.get"' >> ~/.bashrc \
    && echo 'alias compile="mix compile"' >> ~/.bashrc

# Create workspace directory
RUN sudo mkdir -p /workspace && sudo chown $USERNAME:$USERNAME /workspace

# Set working directory
WORKDIR /workspace

# Switch back to root for final setup
USER root

# Install Docker CLI (for Docker-in-Docker)
RUN curl -fsSL https://get.docker.com | sh

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/

# Install kind (Kubernetes in Docker)
RUN curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64 \
    && chmod +x ./kind \
    && mv ./kind /usr/local/bin/kind

# Install helm
RUN curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Install useful development tools
RUN apk add --no-cache \
    # Database tools
    sqlite \
    # Monitoring tools
    prometheus \
    # Text processing
    ripgrep \
    fd \
    # Modern shell tools
    starship \
    && rm -rf /var/cache/apk/*

# Set up starship prompt for better shell experience
USER $USERNAME
RUN echo 'eval "$(starship init bash)"' >> ~/.bashrc

# Create directories for development
RUN mkdir -p ~/.local/bin \
    && mkdir -p ~/.config \
    && mkdir -p ~/bin

# Switch back to vscode user
USER $USERNAME

# Set environment variables
ENV MIX_ENV=dev
ENV PHX_HOST=localhost
ENV PORT=4000
ENV ERL_AFLAGS="-kernel shell_history enabled"

# Expose ports
EXPOSE 4000 4001 9090

# Default command
CMD ["bash"]
