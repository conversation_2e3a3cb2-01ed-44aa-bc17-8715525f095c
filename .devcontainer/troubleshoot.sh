#!/bin/bash

# Phoenix Elixir Dev Container Troubleshooting Script
# This script helps diagnose and fix common dev container issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_check() {
    echo -e "${CYAN}[CHECK]${NC} $1"
}

# Header
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║              Phoenix Dev Container Troubleshooter            ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Step 1: Check Prerequisites
check_prerequisites() {
    log_step "1. Checking Prerequisites"
    echo "================================"
    
    # Check Docker
    log_check "Docker installation and status..."
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        log_success "Docker found: $DOCKER_VERSION"
        
        if docker info &> /dev/null; then
            log_success "Docker daemon is running"
        else
            log_error "Docker daemon is not running. Please start Docker Desktop."
            return 1
        fi
    else
        log_error "Docker is not installed. Please install Docker Desktop."
        return 1
    fi
    
    # Check Docker Compose
    log_check "Docker Compose..."
    if docker compose version &> /dev/null; then
        COMPOSE_VERSION=$(docker compose version)
        log_success "Docker Compose found: $COMPOSE_VERSION"
    elif docker-compose --version &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version)
        log_success "Docker Compose (legacy) found: $COMPOSE_VERSION"
    else
        log_error "Docker Compose not found"
        return 1
    fi
    
    # Check VS Code
    log_check "VS Code installation..."
    if command -v code &> /dev/null; then
        CODE_VERSION=$(code --version | head -1)
        log_success "VS Code found: $CODE_VERSION"
    else
        log_warning "VS Code CLI not found (this is optional)"
    fi
    
    # Check Dev Containers extension
    log_check "Dev Containers extension..."
    if code --list-extensions | grep -q "ms-vscode-remote.remote-containers"; then
        log_success "Dev Containers extension is installed"
    else
        log_warning "Dev Containers extension not found. Please install it from VS Code marketplace."
    fi
    
    echo
}

# Step 2: Validate Configuration Files
validate_config() {
    log_step "2. Validating Configuration Files"
    echo "===================================="
    
    # Check devcontainer.json
    log_check "devcontainer.json syntax..."
    if [ -f ".devcontainer/devcontainer.json" ]; then
        if python3 -m json.tool .devcontainer/devcontainer.json > /dev/null 2>&1; then
            log_success "devcontainer.json is valid JSON"
        else
            log_error "devcontainer.json has syntax errors"
            python3 -m json.tool .devcontainer/devcontainer.json
            return 1
        fi
    else
        log_error "devcontainer.json not found"
        return 1
    fi
    
    # Check docker-compose.yml
    log_check "docker-compose.yml syntax..."
    if [ -f ".devcontainer/docker-compose.yml" ]; then
        if docker compose -f .devcontainer/docker-compose.yml config > /dev/null 2>&1; then
            log_success "docker-compose.yml is valid"
        else
            log_error "docker-compose.yml has syntax errors"
            docker compose -f .devcontainer/docker-compose.yml config
            return 1
        fi
    else
        log_error "docker-compose.yml not found"
        return 1
    fi
    
    # Check Dockerfile
    log_check "Dockerfile syntax..."
    if [ -f ".devcontainer/Dockerfile" ]; then
        if docker build --dry-run -f .devcontainer/Dockerfile .devcontainer > /dev/null 2>&1; then
            log_success "Dockerfile syntax is valid"
        else
            log_warning "Dockerfile may have issues (dry-run not fully supported)"
        fi
    else
        log_error "Dockerfile not found"
        return 1
    fi
    
    # Check script permissions
    log_check "Script permissions..."
    for script in "post-create.sh" "post-attach.sh"; do
        if [ -f ".devcontainer/$script" ]; then
            if [ -x ".devcontainer/$script" ]; then
                log_success "$script is executable"
            else
                log_warning "$script is not executable. Fixing..."
                chmod +x ".devcontainer/$script"
                log_success "Fixed permissions for $script"
            fi
        else
            log_warning "$script not found"
        fi
    done
    
    echo
}

# Step 3: Check Port Conflicts
check_ports() {
    log_step "3. Checking Port Conflicts"
    echo "============================="
    
    PORTS=(4000 4001 5432 6379 8080 8081 8025 9090)
    
    for port in "${PORTS[@]}"; do
        log_check "Port $port..."
        if lsof -i :$port &> /dev/null; then
            PROCESS=$(lsof -i :$port | tail -1 | awk '{print $1, $2}')
            log_warning "Port $port is in use by: $PROCESS"
        else
            log_success "Port $port is available"
        fi
    done
    
    echo
}

# Step 4: Test Docker Services
test_services() {
    log_step "4. Testing Individual Services"
    echo "================================"
    
    cd .devcontainer
    
    # Test PostgreSQL
    log_check "PostgreSQL service..."
    if docker compose up -d postgres; then
        sleep 5
        if docker compose exec postgres pg_isready -U postgres; then
            log_success "PostgreSQL is healthy"
        else
            log_error "PostgreSQL failed to start properly"
            docker compose logs postgres
        fi
    else
        log_error "Failed to start PostgreSQL"
    fi
    
    # Test Redis
    log_check "Redis service..."
    if docker compose up -d redis; then
        sleep 3
        if docker compose exec redis redis-cli ping | grep -q "PONG"; then
            log_success "Redis is healthy"
        else
            log_error "Redis failed to start properly"
            docker compose logs redis
        fi
    else
        log_error "Failed to start Redis"
    fi
    
    # Clean up test services
    docker compose down
    cd ..
    
    echo
}

# Step 5: Test Container Build
test_build() {
    log_step "5. Testing Container Build"
    echo "============================"
    
    log_check "Building development container..."
    cd .devcontainer
    
    if docker compose build phoenix-dev; then
        log_success "Container built successfully"
    else
        log_error "Container build failed"
        return 1
    fi
    
    cd ..
    echo
}

# Step 6: Check System Resources
check_resources() {
    log_step "6. Checking System Resources"
    echo "=============================="
    
    # Check available disk space
    log_check "Disk space..."
    DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 90 ]; then
        log_success "Sufficient disk space available"
    else
        log_warning "Disk space is low ($DISK_USAGE% used)"
    fi
    
    # Check Docker resources
    log_check "Docker resources..."
    if docker system df &> /dev/null; then
        log_info "Docker disk usage:"
        docker system df
    fi
    
    # Check memory
    log_check "Available memory..."
    if command -v free &> /dev/null; then
        MEMORY_INFO=$(free -h | grep "Mem:")
        log_info "Memory: $MEMORY_INFO"
    elif command -v vm_stat &> /dev/null; then
        # macOS
        log_info "Memory information (macOS):"
        vm_stat | head -5
    fi
    
    echo
}

# Step 7: Generate Diagnostic Report
generate_report() {
    log_step "7. Generating Diagnostic Report"
    echo "================================="
    
    REPORT_FILE="devcontainer-diagnostic-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Phoenix Dev Container Diagnostic Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo
        
        echo "System Information:"
        echo "-------------------"
        uname -a
        echo
        
        echo "Docker Information:"
        echo "-------------------"
        docker --version
        docker compose version
        docker info
        echo
        
        echo "VS Code Extensions:"
        echo "-------------------"
        code --list-extensions | grep -E "(remote|docker|elixir|phoenix)" || echo "VS Code CLI not available"
        echo
        
        echo "Configuration Files:"
        echo "--------------------"
        echo "devcontainer.json exists: $([ -f .devcontainer/devcontainer.json ] && echo "Yes" || echo "No")"
        echo "docker-compose.yml exists: $([ -f .devcontainer/docker-compose.yml ] && echo "Yes" || echo "No")"
        echo "Dockerfile exists: $([ -f .devcontainer/Dockerfile ] && echo "Yes" || echo "No")"
        echo
        
        echo "Port Status:"
        echo "------------"
        for port in 4000 4001 5432 6379 8080 8081 8025 9090; do
            if lsof -i :$port &> /dev/null; then
                echo "Port $port: IN USE"
            else
                echo "Port $port: Available"
            fi
        done
        echo
        
        echo "Docker Compose Configuration:"
        echo "-----------------------------"
        cd .devcontainer && docker compose config 2>&1 && cd ..
        echo
        
    } > "$REPORT_FILE"
    
    log_success "Diagnostic report saved to: $REPORT_FILE"
    echo
}

# Step 8: Provide Solutions
provide_solutions() {
    log_step "8. Common Solutions"
    echo "===================="
    
    echo -e "${YELLOW}If you're experiencing issues, try these solutions:${NC}"
    echo
    
    echo "🔧 Container won't start:"
    echo "   • Restart Docker Desktop"
    echo "   • Run: docker system prune -f"
    echo "   • Increase Docker memory to 4GB+ in Docker Desktop settings"
    echo
    
    echo "🔧 Port conflicts:"
    echo "   • Stop conflicting services: sudo lsof -ti:4000 | xargs kill -9"
    echo "   • Modify ports in docker-compose.yml"
    echo
    
    echo "🔧 Build failures:"
    echo "   • Clear Docker cache: docker builder prune -f"
    echo "   • Rebuild without cache: docker compose build --no-cache"
    echo
    
    echo "🔧 VS Code connection issues:"
    echo "   • Reload VS Code window: Cmd/Ctrl + Shift + P → 'Developer: Reload Window'"
    echo "   • Rebuild container: Cmd/Ctrl + Shift + P → 'Dev Containers: Rebuild Container'"
    echo
    
    echo "🔧 Permission issues:"
    echo "   • Fix script permissions: chmod +x .devcontainer/*.sh"
    echo "   • Check Docker socket permissions"
    echo
    
    echo "🔧 Service health issues:"
    echo "   • Check logs: docker compose -f .devcontainer/docker-compose.yml logs"
    echo "   • Restart services: docker compose -f .devcontainer/docker-compose.yml restart"
    echo
}

# Main execution
main() {
    # Run all checks
    check_prerequisites || exit 1
    validate_config || exit 1
    check_ports
    test_services
    test_build
    check_resources
    generate_report
    provide_solutions
    
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    Troubleshooting Complete                  ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    log_info "If issues persist, check the diagnostic report and try the suggested solutions."
}

# Parse arguments
case "${1:-}" in
    --help|-h)
        echo "Phoenix Dev Container Troubleshooter"
        echo "Usage: $0 [--help]"
        echo
        echo "This script diagnoses common dev container issues and provides solutions."
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
