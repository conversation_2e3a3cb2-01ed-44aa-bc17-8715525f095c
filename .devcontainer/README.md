# Phoenix Elixir Dev Container

This directory contains a complete Visual Studio Code Dev Container configuration for Phoenix Elixir development that integrates seamlessly with the local Kubernetes development environment.

## 🎯 Overview

The dev container provides:
- **Isolated Development Environment**: Consistent Elixir/Phoenix setup across all developers
- **Integrated Services**: PostgreSQL and Redis containers that match the k8s/local configuration
- **Pre-configured Tools**: All necessary development tools and VS Code extensions
- **Seamless Integration**: Works with the existing local Kubernetes development workflow

## 📁 File Structure

```
.devcontainer/
├── README.md                   # This documentation
├── devcontainer.json          # Main dev container configuration
├── Dockerfile                 # Custom development container image
├── docker-compose.yml         # Services configuration
├── post-create.sh             # Setup script (runs after container creation)
├── post-attach.sh             # Attach script (runs on each connection)
├── .env.example               # Environment variables template
└── postgres-init/
    └── 01-init.sql            # PostgreSQL initialization script
```

## 🚀 Quick Start

### Prerequisites

1. **Visual Studio Code** with the **Dev Containers** extension installed
2. **Docker Desktop** running on your machine
3. **Git** for cloning the repository

### Getting Started

1. **Open in Dev Container**:
   ```bash
   # Clone your Phoenix project (if not already done)
   git clone <your-phoenix-repo>
   cd <your-phoenix-project>
   
   # Open in VS Code
   code .
   
   # VS Code will detect the dev container configuration
   # Click "Reopen in Container" when prompted
   # Or use Command Palette: "Dev Containers: Reopen in Container"
   ```

2. **Wait for Setup**:
   - The container will build automatically (first time takes 5-10 minutes)
   - Post-create script will install dependencies and set up the database
   - Services (PostgreSQL, Redis) will start automatically

3. **Start Development**:
   ```bash
   # Start Phoenix server
   mix phx.server
   # or use the alias
   phx.server
   
   # Access your app at http://localhost:4000
   ```

## 🛠️ Development Environment

### **Included Tools & Versions**

| Tool | Version | Purpose |
|------|---------|---------|
| **Elixir** | 1.15 | Runtime environment |
| **Erlang/OTP** | Latest | Virtual machine |
| **Phoenix** | Latest | Web framework |
| **Node.js** | Latest LTS | Asset compilation |
| **PostgreSQL** | 14 | Database |
| **Redis** | 7 | Cache/sessions |
| **Docker** | Latest | Container management |
| **kubectl** | Latest | Kubernetes CLI |
| **kind** | Latest | Local Kubernetes |

### **Pre-installed VS Code Extensions**

- **Elixir LS**: Language server for Elixir
- **Phoenix Framework**: Phoenix-specific features
- **PostgreSQL**: Database management
- **Redis Client**: Redis management
- **Docker**: Container management
- **Kubernetes Tools**: K8s integration
- **GitLens**: Enhanced Git features
- **Tailwind CSS**: CSS framework support

### **Development Services**

| Service | URL | Credentials |
|---------|-----|-------------|
| **Phoenix App** | http://localhost:4000 | - |
| **PostgreSQL** | localhost:5432 | postgres/postgres |
| **Redis** | localhost:6379 | No password |
| **pgAdmin** | http://localhost:8080 | <EMAIL>/admin |
| **Redis Commander** | http://localhost:8081 | admin/admin |
| **Mailhog** | http://localhost:8025 | - |

## 🔧 Configuration

### **Environment Variables**

Copy the example environment file and customize:
```bash
cp .devcontainer/.env.example .devcontainer/.env
# Edit .env with your specific configuration
```

Key variables:
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `SECRET_KEY_BASE`: Phoenix secret key
- `PHX_HOST`: Application host (localhost for dev)

### **Database Configuration**

The PostgreSQL container is pre-configured with:
- Database: `phoenix_dev`
- Username: `postgres`
- Password: `postgres`
- Extensions: uuid-ossp, citext, pg_trgm, btree_gin, btree_gist
- Helper functions: `reset_sequence()`, `table_sizes()`

### **VS Code Settings**

The dev container includes optimized VS Code settings:
- Elixir language server configuration
- Auto-formatting on save
- Integrated debugging setup
- Database and Redis client configuration
- Kubernetes tools integration

## 🎮 Development Workflow

### **Common Commands**

The dev container includes helpful aliases:

```bash
# Phoenix commands
phx.server          # Start Phoenix server
phx.routes          # Show routes
phx.digest          # Compile assets

# Database commands
db.create           # Create database
db.migrate          # Run migrations
db.seed             # Seed database
db.reset            # Reset database
db_console          # Open PostgreSQL console

# Testing commands
test                # Run tests
test.watch          # Run tests in watch mode
test.coverage       # Run tests with coverage

# Development helpers
phoenix_console     # Open IEx console
redis_console       # Open Redis CLI
deps.clean          # Clean dependencies
format              # Format code
```

### **Debugging**

1. **Set Breakpoints**: Click in the gutter next to line numbers
2. **Start Debugging**: Press F5 or use "Run and Debug" panel
3. **Debug Configuration**: Pre-configured in `.vscode/launch.json`

### **Database Management**

```bash
# Connect to database
db_console

# Or use pgAdmin web interface
# http://localhost:8080

# Run migrations
mix ecto.migrate

# Reset database
mix ecto.reset
```

### **Testing**

```bash
# Run all tests
mix test

# Run specific test file
mix test test/my_app_web/controllers/page_controller_test.exs

# Run tests with coverage
mix test --cover

# Watch mode (if test.watch is configured)
mix test.watch
```

## 🔗 Integration with Local Kubernetes

The dev container is designed to work alongside the local Kubernetes setup:

### **Accessing K8s Services**

```bash
# Check local k8s status
kubectl get pods -n phoenix-app-local

# Port forward to k8s services
kubectl port-forward service/postgres-service 5433:5432 -n phoenix-app-local

# Use k8s database instead of container database
export DATABASE_URL="ecto://postgres:postgres@localhost:5433/phoenix_dev"
```

### **Development Workflow Options**

1. **Container-Only Development**:
   - Use dev container services (PostgreSQL, Redis)
   - Fastest setup, isolated environment
   - Good for individual development

2. **Hybrid Development**:
   - Use dev container for Phoenix app
   - Connect to k8s services for database/cache
   - Good for testing integration with k8s setup

3. **Full K8s Development**:
   - Use k8s/local setup for all services
   - Use dev container for development tools only
   - Good for production-like development

## 🐛 Troubleshooting

### **Container Won't Start**

```bash
# Check Docker is running
docker ps

# Rebuild container
# Command Palette: "Dev Containers: Rebuild Container"

# Check logs
docker-compose logs -f
```

### **Database Connection Issues**

```bash
# Check PostgreSQL is running
docker-compose ps postgres

# Test connection
pg_isready -h postgres -p 5432 -U postgres

# Check logs
docker-compose logs postgres
```

### **Port Conflicts**

If ports are already in use:
```bash
# Check what's using the port
lsof -i :4000

# Stop conflicting services
# Or modify ports in docker-compose.yml
```

### **Performance Issues**

```bash
# Check resource usage
docker stats

# Increase Docker resources in Docker Desktop settings
# Recommended: 4GB RAM, 2 CPUs minimum
```

### **VS Code Extension Issues**

```bash
# Reload window
# Command Palette: "Developer: Reload Window"

# Reinstall extensions
# Command Palette: "Dev Containers: Rebuild Container"
```

## 📚 Additional Resources

### **Phoenix Development**
- [Phoenix Guides](https://hexdocs.pm/phoenix/overview.html)
- [Elixir Documentation](https://elixir-lang.org/docs.html)
- [Phoenix LiveView](https://hexdocs.pm/phoenix_live_view/)

### **Dev Containers**
- [VS Code Dev Containers](https://code.visualstudio.com/docs/remote/containers)
- [Dev Container Specification](https://containers.dev/)

### **Local Kubernetes Integration**
- [Local K8s Setup](../k8s/local/README.md)
- [Production K8s Setup](../k8s/README.md)

## 🤝 Contributing

When modifying the dev container configuration:

1. **Test thoroughly** with a fresh container build
2. **Update documentation** for any changes
3. **Maintain compatibility** with the local k8s setup
4. **Keep the container lightweight** but functional
5. **Document new tools or extensions** added

## 💡 Tips & Best Practices

### **Performance Optimization**
- Use named volumes for dependencies (`deps`, `_build`, `node_modules`)
- Mount source code with `:cached` flag for better performance
- Exclude build artifacts from file watchers

### **Security**
- Run as non-root user (`vscode`)
- Use development-only secrets and passwords
- Don't commit sensitive data to version control

### **Development Efficiency**
- Use aliases and functions for common commands
- Set up proper shell history and completion
- Configure VS Code settings for your workflow
- Use integrated debugging instead of IO.inspect

### **Collaboration**
- Keep the dev container configuration in version control
- Document any custom setup steps
- Use consistent environment variables across team
- Share useful aliases and functions

This dev container provides a complete, consistent development environment that integrates seamlessly with your Phoenix Elixir application and the local Kubernetes setup!
