version: '3.8'

services:
  # Phoenix development container
  phoenix-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: phoenix-dev-container
    
    # Keep container running
    command: sleep infinity
    
    # Environment variables
    environment:
      # Phoenix configuration
      MIX_ENV: dev
      PHX_HOST: localhost
      PORT: "4000"
      
      # Database configuration
      DATABASE_URL: "ecto://postgres:postgres@postgres:5432/phoenix_dev"
      DB_HOST: postgres
      DB_PORT: "5432"
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: phoenix_dev
      
      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: "6379"
      REDIS_DATABASE: "0"
      REDIS_URL: "redis://redis:6379/0"
      
      # Application settings
      LOG_LEVEL: debug
      ENABLE_TELEMETRY: "true"
      HEALTH_CHECK_PATH: "/health"
      READY_CHECK_PATH: "/ready"
      
      # Phoenix secrets (development only)
      SECRET_KEY_BASE: "your-secret-key-base-for-local-development-change-this-in-production"
      LIVE_VIEW_SIGNING_SALT: "phoenix_live_view_dev"
      GUARDIAN_SECRET_KEY: "your-guardian-secret-key-for-local-development"
      JWT_SECRET: "your-jwt-secret-for-local-development"
      ENCRYPTION_KEY: "your-encryption-key-for-local-development-32-chars"
      
      # Development features
      CORS_ORIGINS: "http://localhost:4000"
      RATE_LIMIT_ENABLED: "false"
      SCHEDULER_ENABLED: "true"
      
      # Monitoring
      PROMETHEUS_METRICS_ENABLED: "true"
      METRICS_PORT: "9090"
      
      # Shell configuration
      ERL_AFLAGS: "-kernel shell_history enabled"
    
    # Port mappings
    ports:
      - "4000:4000"   # Phoenix application
      - "4001:4001"   # Phoenix LiveReload
      - "9090:9090"   # Metrics/Prometheus
    
    # Volume mounts
    volumes:
      # Mount the entire project
      - ..:/workspace:cached
      # Mount Docker socket for Docker-in-Docker
      - /var/run/docker.sock:/var/run/docker.sock
      # Persist Elixir build artifacts
      - elixir_build:/workspace/_build
      - elixir_deps:/workspace/deps
      # Persist node modules
      - node_modules:/workspace/assets/node_modules
      # Persist mix local installs
      - mix_local:/home/<USER>/.mix
      # Persist hex cache
      - hex_cache:/home/<USER>/.hex
      # Persist shell history
      - bash_history:/home/<USER>/.bash_history
      # Persist VS Code extensions
      - vscode_extensions:/home/<USER>/.vscode-server/extensions
    
    # Working directory
    working_dir: /workspace
    
    # User
    user: vscode
    
    # Network
    networks:
      - phoenix-dev-network
    
    # Dependencies
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped

  # PostgreSQL database (matches k8s/local configuration)
  postgres:
    image: postgres:14-alpine
    container_name: phoenix-dev-postgres
    
    environment:
      POSTGRES_DB: phoenix_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGDATA: /var/lib/postgresql/data/pgdata
    
    ports:
      - "5432:5432"
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # Custom initialization scripts
      - ./postgres-init:/docker-entrypoint-initdb.d:ro
    
    networks:
      - phoenix-dev-network
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d phoenix_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    
    restart: unless-stopped

  # Redis cache (matches k8s/local configuration)
  redis:
    image: redis:7-alpine
    container_name: phoenix-dev-redis
    
    command: redis-server --appendonly yes
    
    ports:
      - "6379:6379"
    
    volumes:
      - redis_data:/data
    
    networks:
      - phoenix-dev-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    
    restart: unless-stopped

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: phoenix-dev-pgadmin
    
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: "False"
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: "False"
    
    ports:
      - "8080:80"
    
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    
    networks:
      - phoenix-dev-network
    
    depends_on:
      - postgres
    
    restart: unless-stopped
    
    profiles:
      - tools

  # Redis Commander for Redis management (optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: phoenix-dev-redis-commander
    
    environment:
      REDIS_HOSTS: "local:redis:6379"
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    
    ports:
      - "8081:8081"
    
    networks:
      - phoenix-dev-network
    
    depends_on:
      - redis
    
    restart: unless-stopped
    
    profiles:
      - tools

  # Mailhog for email testing (optional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: phoenix-dev-mailhog
    
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    
    networks:
      - phoenix-dev-network
    
    restart: unless-stopped
    
    profiles:
      - tools

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  elixir_build:
    driver: local
  elixir_deps:
    driver: local
  node_modules:
    driver: local
  mix_local:
    driver: local
  hex_cache:
    driver: local
  bash_history:
    driver: local
  vscode_extensions:
    driver: local

# Network configuration
networks:
  phoenix-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
