# Phoenix Dev Container Troubleshooting Guide

## 🚨 Issue Fixed: JSON Syntax Error

**Problem**: The original `devcontainer.json` contained JSON comments which are not valid JSON syntax.

**Solution**: ✅ **FIXED** - Removed all comments from `devcontainer.json`. The file is now valid JSON.

## 🔧 Quick Diagnostic Commands

Run these commands to check your setup:

```bash
# 1. Run the comprehensive troubleshooter
./.devcontainer/troubleshoot.sh

# 2. Or run the quick fix script
./.devcontainer/quick-fix.sh

# 3. Manual checks
docker --version
docker compose version
python3 -m json.tool .devcontainer/devcontainer.json > /dev/null && echo "JSON valid" || echo "JSON invalid"
docker compose -f .devcontainer/docker-compose.yml config > /dev/null && echo "Compose valid" || echo "Compose invalid"
```

## 🐛 Common Issues & Solutions

### 1. **Container Won't Start**

**Symptoms**: VS Code shows "Failed to start container" or hangs on "Starting Dev Container"

**Solutions**:
```bash
# Restart Docker Desktop
# Then try:
docker system prune -f
docker volume prune -f

# Rebuild without cache
cd .devcontainer
docker compose down -v
docker compose build --no-cache
cd ..
```

### 2. **Port Conflicts**

**Symptoms**: "Port already in use" errors

**Solutions**:
```bash
# Check what's using ports
lsof -i :4000
lsof -i :5432
lsof -i :6379

# Kill conflicting processes
sudo lsof -ti:4000 | xargs kill -9
sudo lsof -ti:5432 | xargs kill -9
sudo lsof -ti:6379 | xargs kill -9

# Or use the quick fix script
./.devcontainer/quick-fix.sh
```

### 3. **VS Code Extension Issues**

**Symptoms**: Extensions not loading, Elixir LS not working

**Solutions**:
```bash
# Reload VS Code window
# Command Palette: "Developer: Reload Window"

# Rebuild container
# Command Palette: "Dev Containers: Rebuild Container"

# Reset VS Code dev container cache
rm -rf ~/.vscode/extensions/ms-vscode-remote.remote-containers-*/dist/dev-containers-cli/cache
```

### 4. **Database Connection Issues**

**Symptoms**: "Database connection failed" or Phoenix can't connect to PostgreSQL

**Solutions**:
```bash
# Check PostgreSQL is running
docker compose -f .devcontainer/docker-compose.yml ps postgres

# Check PostgreSQL logs
docker compose -f .devcontainer/docker-compose.yml logs postgres

# Test connection manually
docker compose -f .devcontainer/docker-compose.yml exec postgres pg_isready -U postgres

# Restart PostgreSQL
docker compose -f .devcontainer/docker-compose.yml restart postgres
```

### 5. **Build Failures**

**Symptoms**: Container build fails with dependency or compilation errors

**Solutions**:
```bash
# Clear Docker build cache
docker builder prune -f

# Rebuild with verbose output
cd .devcontainer
docker compose build --no-cache --progress=plain
cd ..

# Check Dockerfile syntax
docker build --dry-run -f .devcontainer/Dockerfile .devcontainer
```

### 6. **Permission Issues**

**Symptoms**: "Permission denied" errors, scripts won't execute

**Solutions**:
```bash
# Fix script permissions
chmod +x .devcontainer/*.sh

# Check Docker socket permissions (Linux/WSL)
sudo chmod 666 /var/run/docker.sock

# Or add user to docker group
sudo usermod -aG docker $USER
# Then logout and login again
```

## 🔍 Step-by-Step Debugging

### Step 1: Verify Prerequisites
```bash
# Check Docker is running
docker info

# Check Docker Compose
docker compose version

# Check VS Code Dev Containers extension
code --list-extensions | grep ms-vscode-remote.remote-containers
```

### Step 2: Test Individual Components
```bash
# Test PostgreSQL alone
cd .devcontainer
docker compose up -d postgres
docker compose exec postgres pg_isready -U postgres
docker compose down

# Test Redis alone
docker compose up -d redis
docker compose exec redis redis-cli ping
docker compose down
```

### Step 3: Test Container Build
```bash
cd .devcontainer
docker compose build phoenix-dev
```

### Step 4: Check VS Code Logs
1. Open VS Code
2. Command Palette → "Developer: Toggle Developer Tools"
3. Check Console tab for errors
4. Look for dev container specific errors

## 🚀 Alternative Approaches

### Option 1: Minimal Dev Container
If the full setup is problematic, try the minimal version:

```bash
# Backup current config
mv .devcontainer/devcontainer.json .devcontainer/devcontainer-full.json

# Use minimal config
cp .devcontainer/minimal-devcontainer.json .devcontainer/devcontainer.json

# Try opening in container
```

### Option 2: Use Local Services
Connect to local Kubernetes services instead:

```bash
# Start local k8s services
cd k8s/local
./scripts/setup-local.sh

# Use minimal dev container with external services
export DATABASE_URL="ecto://postgres:postgres@localhost:30432/phoenix_dev"
export REDIS_URL="redis://localhost:30379/0"
```

### Option 3: Docker Compose Only
Skip VS Code dev containers entirely:

```bash
# Use standalone Docker Compose
cd .devcontainer
docker compose up -d

# Connect VS Code to running container
# Command Palette: "Dev Containers: Attach to Running Container"
```

## 📊 System Requirements

**Minimum Requirements**:
- Docker Desktop with 4GB RAM allocated
- 10GB free disk space
- VS Code with Dev Containers extension

**Recommended**:
- Docker Desktop with 8GB RAM allocated
- 20GB free disk space
- Fast SSD storage

## 🆘 Getting Help

If issues persist:

1. **Run the diagnostic script**:
   ```bash
   ./.devcontainer/troubleshoot.sh
   ```

2. **Check the generated report**: `devcontainer-diagnostic-*.txt`

3. **Common VS Code Commands**:
   - `Dev Containers: Rebuild Container`
   - `Dev Containers: Rebuild Container Without Cache`
   - `Developer: Reload Window`
   - `Developer: Show Logs`

4. **Docker Commands**:
   ```bash
   # View all containers
   docker ps -a
   
   # View container logs
   docker logs <container-name>
   
   # Clean everything
   docker system prune -a --volumes
   ```

## ✅ Success Indicators

You'll know it's working when:
- ✅ VS Code opens the dev container without errors
- ✅ Terminal shows the custom prompt with Elixir info
- ✅ `mix --version` works in the terminal
- ✅ Database connection works: `mix ecto.create`
- ✅ Phoenix server starts: `mix phx.server`
- ✅ App accessible at http://localhost:4000

## 🔄 Reset Everything

If all else fails, complete reset:

```bash
# Stop all containers
docker stop $(docker ps -aq)

# Remove all containers
docker rm $(docker ps -aq)

# Remove all images
docker rmi $(docker images -q)

# Remove all volumes
docker volume rm $(docker volume ls -q)

# Clean system
docker system prune -a --volumes

# Restart Docker Desktop
# Then try the dev container again
```
