{
  "name": "Phoenix Elixir Development (Minimal)",
  "image": "elixir:1.15-alpine",
  "workspaceFolder": "/workspace",
  
  // Minimal features
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {}
  },

  // Essential VS Code extensions only
  "customizations": {
    "vscode": {
      "extensions": [
        "jakebecker.elixir-ls",
        "ms-azuretools.vscode-docker"
      ],
      "settings": {
        "elixirLS.dialyzerEnabled": false,
        "elixirLS.fetchDeps": true,
        "editor.formatOnSave": true,
        "files.associations": {
          "*.ex": "elixir",
          "*.exs": "elixir",
          "*.eex": "phoenix-heex",
          "*.heex": "phoenix-heex"
        }
      }
    }
  },

  // Forward essential ports only
  "forwardPorts": [4000],
  
  // Simplified environment
  "containerEnv": {
    "MIX_ENV": "dev",
    "PORT": "4000"
  },

  // Install basic tools
  "postCreateCommand": "apk add --no-cache build-base git curl nodejs npm postgresql-client && mix local.hex --force && mix local.rebar --force",

  // Run as root for simplicity
  "remoteUser": "root"
}
