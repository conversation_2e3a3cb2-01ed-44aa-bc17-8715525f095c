# Phoenix Elixir Dev Container Environment Variables
# Copy this file to .env and customize as needed

# =============================================================================
# Phoenix Application Configuration
# =============================================================================

# Environment
MIX_ENV=dev
PHX_HOST=localhost
PORT=4000

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL connection
DATABASE_URL=ecto://postgres:postgres@postgres:5432/phoenix_dev
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=phoenix_dev

# Database pool configuration
DB_POOL_SIZE=10
DB_TIMEOUT=15000
DB_QUEUE_TARGET=50
DB_QUEUE_INTERVAL=1000

# =============================================================================
# Cache Configuration (Redis)
# =============================================================================

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=

# Cache settings
CACHE_ADAPTER=Nebulex.Adapters.Redis
CACHE_TTL=3600

# =============================================================================
# Application Secrets (Development Only)
# =============================================================================

# Phoenix secret key base (generate with: mix phx.gen.secret)
SECRET_KEY_BASE=your-secret-key-base-for-local-development-change-this-in-production

# LiveView signing salt
LIVE_VIEW_SIGNING_SALT=phoenix_live_view_dev

# Guardian secret key (if using Guardian for authentication)
GUARDIAN_SECRET_KEY=your-guardian-secret-key-for-local-development

# JWT secret (if using JWT tokens)
JWT_SECRET=your-jwt-secret-for-local-development

# Encryption key for sensitive data (32 characters)
ENCRYPTION_KEY=your-encryption-key-for-local-development-32-chars

# =============================================================================
# Development Features
# =============================================================================

# Logging
LOG_LEVEL=debug

# Telemetry and monitoring
ENABLE_TELEMETRY=true
PROMETHEUS_METRICS_ENABLED=true
METRICS_PORT=9090

# CORS (for development)
CORS_ORIGINS=http://localhost:4000

# Rate limiting (disabled for development)
RATE_LIMIT_ENABLED=false

# Background job scheduler
SCHEDULER_ENABLED=true

# =============================================================================
# Email Configuration (Development)
# =============================================================================

# Mailhog SMTP settings (for email testing)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_SSL=false
SMTP_TLS=false

# Email settings
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# =============================================================================
# External Services (Development)
# =============================================================================

# API keys for external services (development/test keys)
EXTERNAL_API_KEY=your-external-api-key-for-development

# OAuth configuration (if using OAuth)
OAUTH_CLIENT_ID=your-oauth-client-id-for-development
OAUTH_CLIENT_SECRET=your-oauth-client-secret-for-development

# =============================================================================
# File Storage (Development)
# =============================================================================

# Local file storage
UPLOADS_PATH=/workspace/uploads
STATIC_PATH=/workspace/priv/static

# =============================================================================
# Development Tools Configuration
# =============================================================================

# pgAdmin
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin

# Redis Commander
REDIS_COMMANDER_USER=admin
REDIS_COMMANDER_PASSWORD=admin

# =============================================================================
# Erlang/Elixir Configuration
# =============================================================================

# Enable shell history in IEx
ERL_AFLAGS=-kernel shell_history enabled

# Erlang distribution (if needed for clustering)
# ERLANG_COOKIE=your-erlang-cookie-for-development

# =============================================================================
# Node.js/Asset Configuration
# =============================================================================

# Node environment
NODE_ENV=development

# Asset compilation
ASSETS_PATH=/workspace/assets

# =============================================================================
# Testing Configuration
# =============================================================================

# Test database (if different from development)
# TEST_DATABASE_URL=ecto://postgres:postgres@postgres:5432/phoenix_test

# =============================================================================
# Docker/Container Configuration
# =============================================================================

# Container user
CONTAINER_USER=vscode
CONTAINER_UID=1000
CONTAINER_GID=1000

# =============================================================================
# Kubernetes Integration (Optional)
# =============================================================================

# Kubernetes namespace for local development
K8S_NAMESPACE=phoenix-app-local

# Kubernetes context
K8S_CONTEXT=docker-desktop

# =============================================================================
# Development Workflow
# =============================================================================

# Auto-reload settings
LIVE_RELOAD_ENABLED=true
LIVE_RELOAD_PORT=4001

# Code formatting
AUTO_FORMAT_ON_SAVE=true

# =============================================================================
# Security (Development Only)
# =============================================================================

# Disable security features for easier development
SECURE_COOKIES=false
FORCE_SSL=false

# =============================================================================
# Custom Application Variables
# =============================================================================

# Add your custom application-specific environment variables here
# CUSTOM_FEATURE_ENABLED=true
# CUSTOM_API_ENDPOINT=http://localhost:3000/api
